using Furion.ConfigurableOptions;

namespace APIFurion.Options;

/// <summary>
/// 天气设置配置
/// </summary>
public class WeatherSettings : IConfigurableOptions
{
    /// <summary>
    /// 默认天数
    /// </summary>
    public int DefaultDays { get; set; } = 5;

    /// <summary>
    /// 最大天数
    /// </summary>
    public int MaxDays { get; set; } = 30;

    /// <summary>
    /// API名称
    /// </summary>
    public string ApiName { get; set; } = "Weather API";

    /// <summary>
    /// 版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";
}
