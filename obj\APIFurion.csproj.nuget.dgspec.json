{"format": 1, "restore": {"E:\\练习\\APIFurion\\APIFurion\\APIFurion.csproj": {}}, "projects": {"E:\\练习\\APIFurion\\APIFurion\\APIFurion.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\练习\\APIFurion\\APIFurion\\APIFurion.csproj", "projectName": "APIFurion", "projectPath": "E:\\练习\\APIFurion\\APIFurion\\APIFurion.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\练习\\APIFurion\\APIFurion\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.nuget.org/api/v2/": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Furion": {"target": "Package", "version": "[4.9.7.106, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.17, )"}, "NetTopologySuite": {"target": "Package", "version": "[2.5.0, )"}, "NetTopologySuite.IO.ShapeFile": {"target": "Package", "version": "[2.1.0, )"}, "ProjNET": {"target": "Package", "version": "[2.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[9.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}