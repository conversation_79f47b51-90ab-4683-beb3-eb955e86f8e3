### 测试API端点

### 1. 获取API信息
GET http://localhost:5089/api/info
Accept: application/json

### 2. 健康检查
GET http://localhost:5089/api/info/health
Accept: application/json

### 3. 获取默认天气预报（5天）
GET http://localhost:5089/api/weather
Accept: application/json

### 4. 获取指定天数的天气预报（10天）
GET http://localhost:5089/api/weather?days=10
Accept: application/json

### 5. 获取指定ID的天气预报
GET http://localhost:5089/api/weather/3
Accept: application/json

### 6. 测试错误情况 - 超出最大天数
GET http://localhost:5089/api/weather?days=50
Accept: application/json

### 7. 测试错误情况 - 无效ID
GET http://localhost:5089/api/weather/999
Accept: application/json

### 8. 获取当前IP信息
GET http://localhost:5089/api/log/current-ip
Accept: application/json

### 9. 获取访问统计信息
GET http://localhost:5089/api/log/statistics
Accept: application/json

### 10. 获取日志文件列表
GET http://localhost:5089/api/log/files
Accept: application/json

### 11. 获取今天的访问日志
GET http://localhost:5089/api/log/access/2025-07-30
Accept: application/json

### 12. 测试写入日志
POST http://localhost:5089/api/log/test
Content-Type: application/json

"这是一条测试日志消息"

### === 道路渗透率统计API测试 ===

### 13. 获取道路渗透率统计
POST http://localhost:5089/api/roadpenetration/statistics
Content-Type: application/json

{
  "pageIndex": 1,
  "pageSize": 10
}

### 14. 根据道路ID获取渗透率详情
GET http://localhost:5089/api/roadpenetration/G001
Accept: application/json

### 15. 添加道路渗透率数据
POST http://localhost:5089/api/roadpenetration
Content-Type: application/json

{
  "roadId": "TEST001",
  "roadName": "测试道路",
  "roadLength": 50.5,
  "smartDeviceCount": 30,
  "traditionalDeviceCount": 10,
  "roadType": 5,
  "region": "测试区域"
}

### 16. 从Shapefile分析道路渗透率
POST http://localhost:5089/api/geospatial/analyze-road-penetration
Content-Type: application/json

{
  "roadLayerPath": "Data/Roads/roads.shp",
  "deviceDataSource": "Data/Devices/sample_devices.csv",
  "regionName": "北京市",
  "roadTypes": [1, 2, 3],
  "deviceTypes": [1, 2],
  "minCoverageRate": 0.0,
  "maxCoverageRate": 100.0
}

### 17. 计算区域渗透率统计
POST http://localhost:5089/api/geospatial/region-statistics
Content-Type: application/json

{
  "regionName": "北京市",
  "roadLayerPath": "Data/Roads/roads.shp",
  "deviceDataSource": "Data/Devices/sample_devices.csv",
  "roadTypes": [1, 2, 3],
  "deviceTypes": [1, 2],
  "includeDetailedAnalysis": true
}

### 18. 加载道路数据
POST http://localhost:5089/api/geospatial/load-roads
Content-Type: application/json

{
  "shapefilePath": "Data/Roads/roads.shp",
  "regionFilter": "北京市",
  "roadTypeFilter": [1, 2, 3],
  "includeGeometry": false,
  "maxRecords": 1000
}

### 19. 加载设备数据
POST http://localhost:5089/api/geospatial/load-devices
Content-Type: application/json

{
  "deviceDataSource": "Data/Devices/sample_devices.csv",
  "deviceTypeFilter": [1, 2],
  "onlineOnly": false,
  "minCoverageRadius": 50.0,
  "maxCoverageRadius": 500.0,
  "maxRecords": 1000
}

### 20. 测试不同编码加载道路数据
POST http://localhost:5089/api/geospatial/test-encoding
Content-Type: application/json

{
  "shapefilePath": "Data/Roads/roads.shp",
  "maxRecords": 10,
  "detailedOutput": true
}

### 16. 获取区域渗透率汇总
GET http://localhost:5089/api/roadpenetration/region/华东地区/summary
Accept: application/json

### 17. 获取道路类型渗透率汇总
GET http://localhost:5089/api/roadpenetration/roadtype/1/summary
Accept: application/json

### 18. 获取道路类型枚举列表
GET http://localhost:5089/api/roadpenetration/road-types
Accept: application/json

### 19. 批量导入道路渗透率数据
POST http://localhost:5089/api/roadpenetration/batch-import
Content-Type: application/json

[
  {
    "roadId": "BATCH001",
    "roadName": "批量测试道路1",
    "roadLength": 25.0,
    "smartDeviceCount": 15,
    "traditionalDeviceCount": 5,
    "roadType": 3,
    "region": "测试区域"
  },
  {
    "roadId": "BATCH002",
    "roadName": "批量测试道路2",
    "roadLength": 30.0,
    "smartDeviceCount": 20,
    "traditionalDeviceCount": 8,
    "roadType": 4,
    "region": "测试区域"
  }
]
