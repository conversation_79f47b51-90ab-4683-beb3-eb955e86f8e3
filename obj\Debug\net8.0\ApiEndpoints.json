[{"ContainingType": "APIFurion.Controllers.GeospatialController", "Method": "AnalyzeRoadPenetrationFromShapefile", "RelativePath": "api/Geospatial/analyze-road-penetration", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "APIFurion.Models.RoadPenetrationAnalysisRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "APIFurion.Controllers.GeospatialController", "Method": "LoadRoadData", "RelativePath": "api/Geospatial/GetRoad", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "APIFurion.Models.LoadRoadDataRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "APIFurion.Controllers.GeospatialController", "Method": "LoadDeviceData", "RelativePath": "api/Geospatial/load-devices", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "APIFurion.Models.LoadDeviceDataRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "APIFurion.Controllers.GeospatialController", "Method": "CalculateRegionStatistics", "RelativePath": "api/Geospatial/region-statistics", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "APIFurion.Models.RegionStatisticsRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "APIFurion.Controllers.GeospatialController", "Method": "TestEncodingLoadRoadData", "RelativePath": "api/Geospatial/test-encoding", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "APIFurion.Models.TestEncodingRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "APIFurion.Controllers.InfoController", "Method": "GetInfo", "RelativePath": "api/Info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "APIFurion.Controllers.InfoController", "Method": "HealthCheck", "RelativePath": "api/Info/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "APIFurion.Controllers.LogController", "Method": "GetAccessLog", "RelativePath": "api/Log/access/{date}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "date", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "APIFurion.Controllers.LogController", "Method": "GetCurrentIp", "RelativePath": "api/Log/current-ip", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "APIFurion.Controllers.LogController", "Method": "GetLogFiles", "RelativePath": "api/Log/files", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "APIFurion.Controllers.LogController", "Method": "GetAccessStatistics", "RelativePath": "api/Log/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "APIFurion.Controllers.LogController", "Method": "TestLog", "RelativePath": "api/Log/test", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "message", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "APIFurion.Controllers.WeatherController", "Method": "Get", "RelativePath": "api/Weather", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "days", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[APIFurion.Controllers.WeatherForecast, APIFurion, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "APIFurion.Controllers.WeatherController", "Method": "GetById", "RelativePath": "api/Weather/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "APIFurion.Controllers.WeatherForecast", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "APIFurion.Controllers.RoadPenetrationController", "Method": "GetPenetrationStatistics", "RelativePath": "RoadPenetration/GetPenetrationStatistics", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "APIFurion.Models.PenetrationStatisticsRequest", "IsRequired": true}], "ReturnTypes": []}]