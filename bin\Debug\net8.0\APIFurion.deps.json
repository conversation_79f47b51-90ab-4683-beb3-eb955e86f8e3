{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"APIFurion/1.0.0": {"dependencies": {"Furion": "*********", "Microsoft.AspNetCore.OpenApi": "8.0.17", "NetTopologySuite": "2.5.0", "NetTopologySuite.IO.ShapeFile": "2.1.0", "ProjNET": "2.0.0", "Swashbuckle.AspNetCore": "9.0.3"}, "runtime": {"APIFurion.dll": {}}}, "Ben.Demystifier/0.4.1": {"dependencies": {"System.Reflection.Metadata": "7.0.0"}, "runtime": {"lib/netstandard2.1/Ben.Demystifier.dll": {"assemblyVersion": "0.4.0.0", "fileVersion": "0.4.0.2"}}}, "Furion/*********": {"dependencies": {"Furion.Extras.DependencyModel.CodeAnalysis": "*********", "Swashbuckle.AspNetCore": "9.0.3"}, "runtime": {"lib/net8.0/Furion.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Furion.Extras.DependencyModel.CodeAnalysis/*********": {"dependencies": {"Ben.Demystifier": "0.4.1", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.18", "Microsoft.AspNetCore.Razor.Language": "6.0.36", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.18", "Microsoft.Extensions.DependencyModel": "8.0.2"}, "runtime": {"lib/net8.0/Furion.Extras.DependencyModel.CodeAnalysis.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Microsoft.AspNetCore.JsonPatch/8.0.18": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1825.31706"}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/8.0.18": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "8.0.18", "Newtonsoft.Json": "13.0.3", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1825.31706"}}}, "Microsoft.AspNetCore.OpenApi/8.0.17": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "8.0.17.0", "fileVersion": "8.0.1725.26609"}}}, "Microsoft.AspNetCore.Razor.Language/6.0.36": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "6.0.36.0", "fileVersion": "6.0.3624.51604"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "7.0.0", "System.Reflection.Metadata": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "4.8.0.0", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "4.8.0.0", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.EntityFrameworkCore/8.0.18": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.18", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.18", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1825.31203"}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.18": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1825.31203"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.18": {}, "Microsoft.EntityFrameworkCore.Relational/8.0.18": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.18", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1825.31203"}}}, "Microsoft.Extensions.ApiDescription.Server/8.0.0": {}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyModel/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "8.0.0.2", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Options/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.224.6711"}}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.NETCore.Platforms/2.1.2": {}, "Microsoft.OpenApi/1.6.23": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.23.0", "fileVersion": "1.6.23.0"}}}, "NetTopologySuite/2.5.0": {"dependencies": {"System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.0/NetTopologySuite.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NetTopologySuite.Features/2.0.0": {"dependencies": {"NetTopologySuite": "2.5.0"}, "runtime": {"lib/netstandard2.0/NetTopologySuite.Features.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NetTopologySuite.IO.ShapeFile/2.1.0": {"dependencies": {"NetTopologySuite": "2.5.0", "NetTopologySuite.Features": "2.0.0", "System.Text.Encoding.CodePages": "4.5.1"}, "runtime": {"lib/netstandard2.0/NetTopologySuite.IO.ShapeFile.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}}, "ProjNET/2.0.0": {"dependencies": {"System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0"}, "runtime": {"lib/netstandard2.0/ProjNET.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore/9.0.3": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "8.0.0", "Swashbuckle.AspNetCore.Swagger": "9.0.3", "Swashbuckle.AspNetCore.SwaggerGen": "9.0.3", "Swashbuckle.AspNetCore.SwaggerUI": "9.0.3"}}, "Swashbuckle.AspNetCore.Swagger/9.0.3": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.3": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "9.0.3"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.3": {"runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}}, "System.Collections.Immutable/7.0.0": {}, "System.Memory/4.5.4": {}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection.Metadata/7.0.0": {"dependencies": {"System.Collections.Immutable": "7.0.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Encoding.CodePages/4.5.1": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}}}}, "libraries": {"APIFurion/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Ben.Demystifier/0.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-axFeEMfmEORy3ipAzOXG/lE+KcNptRbei3F0C4kQCdeiQtW+qJW90K5iIovITGrdLt8AjhNCwk5qLSX9/rFpoA==", "path": "ben.demystifier/0.4.1", "hashPath": "ben.demystifier.0.4.1.nupkg.sha512"}, "Furion/*********": {"type": "package", "serviceable": true, "sha512": "sha512-T7sgb6hXb9FG5XC6OE4Ow9DAXAKduhBoU4WlYsHsJiWhntAQbMKHtE3qME65KegNr7BD/LQBspLfg2eXC2j+xA==", "path": "furion/*********", "hashPath": "furion.*********.nupkg.sha512"}, "Furion.Extras.DependencyModel.CodeAnalysis/*********": {"type": "package", "serviceable": true, "sha512": "sha512-CvxcWASMNtMQFeg/AS2pePmSRo6j8CTCs3Sxzo3kyl9j2l5TqpwXBMjR5fzE+qDD0esz/yRzsEXxlIO2SKCQ/Q==", "path": "furion.extras.dependencymodel.codeanalysis/*********", "hashPath": "furion.extras.dependencymodel.codeanalysis.*********.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-1c8SQxSYwhxqndMn5prT2h5gPjPz+zZ5bVE+RSmbmX/1Xub9Rf5r3xxWPw9vxW6Uo7bqCdvfs2sBmad+wva4ug==", "path": "microsoft.aspnetcore.jsonpatch/8.0.18", "hashPath": "microsoft.aspnetcore.jsonpatch.8.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-euTSUwsFr33Daw8g3BZPWJmB/PEwJhVYTYfK5/DqiukPkXCwXZhtRY0QZSuSSSApofv3n2Mdah58T2MVT1JvvA==", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/8.0.18", "hashPath": "microsoft.aspnetcore.mvc.newtonsoftjson.8.0.18.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/8.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-sGed2RDkJYAoi7N4gUTota56Mix/Vu6UrzCWdwqH1jZwnLnZb+eP2zdABYCaVlBEHBwzuL1zchUFtJXW4lO5LA==", "path": "microsoft.aspnetcore.openapi/8.0.17", "hashPath": "microsoft.aspnetcore.openapi.8.0.17.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-n5Mg5D0aRrhHJJ6bJcwKqQydIFcgUq0jTlvuynoJjwA2IvAzh8Aqf9cpYagofQbIlIXILkCP6q6FgbngyVtpYA==", "path": "microsoft.aspnetcore.razor.language/6.0.36", "hashPath": "microsoft.aspnetcore.razor.language.6.0.36.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "path": "microsoft.codeanalysis.common/4.8.0", "hashPath": "microsoft.codeanalysis.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "path": "microsoft.codeanalysis.csharp/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-LBc07vlgPxEXmjF0Kgn1S0mip3KLDPVD1OQOFu+4Mfpg1Z8OPMJ82MVCkqek1Ex2WeCzVGbNI9nRXcepHB+48g==", "path": "microsoft.entityframeworkcore/8.0.18", "hashPath": "microsoft.entityframeworkcore.8.0.18.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-aQGpxj0/RKXhSqDFbWENQgOg6WQH3z5Dezu3VBXaTCBHE6hAWQIZmmqdpO1k+lkANsoCSwPJZ4iFRqPPZXBXzg==", "path": "microsoft.entityframeworkcore.abstractions/8.0.18", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.18.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-aYkyWRkb+o9++mtIWn5XSYPVND5N9mFFfvdmBX1s6kCss6XTaZsFXf8QjvaiXAcGblp/HoYzS5lusx0ZqeFxzQ==", "path": "microsoft.entityframeworkcore.analyzers/8.0.18", "hashPath": "microsoft.entityframeworkcore.analyzers.8.0.18.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/8.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-SL067ITd6QfDF9wNsNtGm3fROpnv3SNrOY3Fjb+efEUnKn5NI0sUitrtpUim+t1DtCJIs7qgmyCPdD3zjSt4Xw==", "path": "microsoft.entityframeworkcore.relational/8.0.18", "hashPath": "microsoft.entityframeworkcore.relational.8.0.18.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jDM3a95WerM8g6IcMiBXq1qRS9dqmEUpgnCk2DeMWpPkYtp1ia+CkXabOnK93JmhVlUmv8l9WMPsCSUm+WqkIA==", "path": "microsoft.extensions.apidescription.server/8.0.0", "hashPath": "microsoft.extensions.apidescription.server.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-HFDnhYLccngrzyGgHkjEDU5FMLn4MpOsr5ElgsBMC4yx6lJh4jeWO7fHS8+TXPq+dgxCmUa/Trl8svObmwW4QA==", "path": "microsoft.extensions.caching.memory/8.0.1", "hashPath": "microsoft.extensions.caching.memory.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "path": "microsoft.extensions.dependencyinjection/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-mUBDZZRgZrSyFOsJ2qJJ9fXfqd/kXJwf3AiDoqLD9m6TjY5OO/vLNOb9fb4juC0487eq4hcGN/M2Rh/CKS7QYw==", "path": "microsoft.extensions.dependencymodel/8.0.2", "hashPath": "microsoft.extensions.dependencymodel.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4x+pzsQEbqxhNf1QYRr5TDkLP9UsLT3A6MdRKDDEgrW7h1ljiEPgTNhKYUhNCCAaVpQECVQ+onA91PTPnIp6Lw==", "path": "microsoft.extensions.logging/8.0.1", "hashPath": "microsoft.extensions.logging.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-nroMDjS7hNBPtkZqVBbSiQaQjWRDxITI8Y7XnDs97rqG3EbzVTNLZQf7bIeUJcaHOV8bca47s1Uxq94+2oGdxA==", "path": "microsoft.extensions.logging.abstractions/8.0.2", "hashPath": "microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "path": "microsoft.extensions.options/8.0.2", "hashPath": "microsoft.extensions.options.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-mOJy3M0UN+LUG21dLGMxaWZEP6xYpQEpLuvuEQBaownaX4YuhH6NmNUlN9si+vNkAS6dwJ//N1O4DmLf2CikVg==", "path": "microsoft.netcore.platforms/2.1.2", "hashPath": "microsoft.netcore.platforms.2.1.2.nupkg.sha512"}, "Microsoft.OpenApi/1.6.23": {"type": "package", "serviceable": true, "sha512": "sha512-tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g==", "path": "microsoft.openapi/1.6.23", "hashPath": "microsoft.openapi.1.6.23.nupkg.sha512"}, "NetTopologySuite/2.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-5/+2O2ADomEdUn09mlSigACdqvAf0m/pVPGtIPEPQWnyrVykYY0NlfXLIdkMgi41kvH9kNrPqYaFBTZtHYH7Xw==", "path": "nettopologysuite/2.5.0", "hashPath": "nettopologysuite.2.5.0.nupkg.sha512"}, "NetTopologySuite.Features/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RDbEpujDL0bsAyTMHlbKj36KIPhUrtSGUaO/jUjMnsLcixrJFwoDJ8p717FdgMHpQDrcEJAKF+QsAlYsSULkew==", "path": "nettopologysuite.features/2.0.0", "hashPath": "nettopologysuite.features.2.0.0.nupkg.sha512"}, "NetTopologySuite.IO.ShapeFile/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-syaBMB56Rm1hmEK5vTpr5t/UjS2PA5bsVKaeWYY0ib6dlVonCgxfahxrZchnFlwZvuXnNKy/OP15Tr7UD/BM7Q==", "path": "nettopologysuite.io.shapefile/2.1.0", "hashPath": "nettopologysuite.io.shapefile.2.1.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "ProjNET/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iMJG8qpGJ8SjFrB044O8wgo0raAWCdG1Bvly0mmVcjzsrexDHhC+dUct6Wb1YwQtupMBjSTWq7Fn00YeNErprA==", "path": "projnet/2.0.0", "hashPath": "projnet.2.0.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-Akk4oFgy0ST8Q8pZTfPbrt045tWNyMMiKhlbYjG3qnjQZLz645IL5vhQm7NLicc2sAAQ+vftArIlsYWFevmb2g==", "path": "swashbuckle.aspnetcore/9.0.3", "hashPath": "swashbuckle.aspnetcore.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-CGpkZDWj1g/yH/0wYkxUtBhiFo5TY/Esq2fS0vlBvLOs1UL2Jzef9tdtYmTdd3zBPtnMyXQcsXjMt9yCxz4VaA==", "path": "swashbuckle.aspnetcore.swagger/9.0.3", "hashPath": "swashbuckle.aspnetcore.swagger.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-STqjhw1TZiEGmIRgE6jcJUOcgU/Fjquc6dP4GqbuwBzqWZAWr/9T7FZOGWYEwKnmkMplzlUNepGHwnUrfTP0fw==", "path": "swashbuckle.aspnetcore.swaggergen/9.0.3", "hashPath": "swashbuckle.aspnetcore.swaggergen.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-DgJKJASz5OAygeKv2+N0FCZVhQylESqLXrtrRAqIT0vKpX7t5ImJ1FL6+6OqxKiamGkL0jchRXR8OgpMSsMh8w==", "path": "swashbuckle.aspnetcore.swaggerui/9.0.3", "hashPath": "swashbuckle.aspnetcore.swaggerui.9.0.3.nupkg.sha512"}, "System.Collections.Immutable/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dQPcs0U1IKnBdRDBkrCTi1FoajSTBzLcVTpjO4MBCMC7f4pDOIPzgBoX8JjG7X6uZRJ8EBxsi8+DR1JuwjnzOQ==", "path": "system.collections.immutable/7.0.0", "hashPath": "system.collections.immutable.7.0.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection.Metadata/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MclTG61lsD9sYdpNz9xsKBzjsmsfCtcMZYXz/IUr2zlhaTaABonlr1ESeompTgM+Xk+IwtGYU7/voh3YWB/fWw==", "path": "system.reflection.metadata/7.0.0", "hashPath": "system.reflection.metadata.7.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-4J2JQXbftjPMppIHJ7IC+VXQ9XfEagN92vZZNoG12i+zReYlim5dMoXFC1Zzg7tsnKDM7JPo5bYfFK4Jheq44w==", "path": "system.text.encoding.codepages/4.5.1", "hashPath": "system.text.encoding.codepages.4.5.1.nupkg.sha512"}}}