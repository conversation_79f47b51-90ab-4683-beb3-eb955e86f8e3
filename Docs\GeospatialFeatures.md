# 地理信息处理功能说明

本项目使用NetTopologySuite和ProjNet等库替代MapWinGIS.Shape，提供强大的地理信息处理功能。

## 主要功能

### 1. 道路数据处理
- 从Shapefile加载道路几何数据
- 支持多种道路类型（高速公路、国道、省道、县道、城市道路）
- 计算道路长度和几何属性
- 道路空间查询和过滤

### 2. 设备位置管理
- 从CSV文件加载设备位置数据
- 支持智能设备和传统设备分类
- 设备覆盖范围计算
- 设备状态监控

### 3. 渗透率分析
- 计算道路智能设备渗透率
- 分析设备对道路的覆盖情况
- 识别未覆盖的道路段
- 计算重复覆盖区域

### 4. 空间分析
- 缓冲区分析
- 空间交集、并集、差集运算
- 距离计算
- 点在多边形内判断

## API接口

### 1. 道路渗透率分析
```http
POST /api/geospatial/analyze-road-penetration
Content-Type: application/json

{
  "roadLayerPath": "Data/Roads/roads.shp",
  "deviceDataSource": "Data/Devices/devices.csv",
  "regionName": "北京市",
  "roadTypes": [1, 2, 3],
  "deviceTypes": [1, 2],
  "minCoverageRate": 0.0,
  "maxCoverageRate": 100.0
}
```

### 2. 区域统计
```http
POST /api/geospatial/region-statistics
Content-Type: application/json

{
  "regionName": "北京市",
  "roadLayerPath": "Data/Roads/roads.shp",
  "deviceDataSource": "Data/Devices/devices.csv",
  "roadTypes": [1, 2, 3],
  "deviceTypes": [1, 2],
  "includeDetailedAnalysis": true
}
```

### 3. 加载道路数据
```http
POST /api/geospatial/load-roads
Content-Type: application/json

{
  "shapefilePath": "Data/Roads/roads.shp",
  "regionFilter": "北京市",
  "roadTypeFilter": [1, 2, 3],
  "includeGeometry": false,
  "maxRecords": 1000
}
```

### 4. 加载设备数据
```http
POST /api/geospatial/load-devices
Content-Type: application/json

{
  "deviceDataSource": "Data/Devices/devices.csv",
  "deviceTypeFilter": [1, 2],
  "onlineOnly": false,
  "minCoverageRadius": 50.0,
  "maxCoverageRadius": 500.0,
  "maxRecords": 1000
}
```

## 数据格式

### Shapefile道路数据
道路Shapefile应包含以下属性字段：
- `ROAD_ID` 或 `ID`: 道路唯一标识
- `ROAD_NAME` 或 `NAME`: 道路名称
- `ROAD_TYPE` 或 `TYPE`: 道路类型（1-5）
- `REGION`: 所属区域

### CSV设备数据
设备CSV文件格式：
```csv
DeviceId,DeviceName,Longitude,Latitude,DeviceType,CoverageRadius,InstallTime,IsOnline
DEV001,智能监控设备001,116.3974,39.9093,Smart,150,2023-01-15 10:30:00,true
```

字段说明：
- `DeviceId`: 设备唯一标识
- `DeviceName`: 设备名称
- `Longitude`: 经度
- `Latitude`: 纬度
- `DeviceType`: 设备类型（Smart/Traditional）
- `CoverageRadius`: 覆盖半径（米）
- `InstallTime`: 安装时间
- `IsOnline`: 是否在线

## 配置说明

在`appsettings.json`中配置：
```json
{
  "AppSettings": {
    "MainDBStr": "数据库连接字符串",
    "TaskDBStr": "任务数据库连接字符串",
    "RoadLayerPath": "Data/Roads/roads.shp"
  }
}
```

## 使用示例

### 1. 基本渗透率分析
```csharp
// 注入服务
private readonly IGeospatialService _geospatialService;
private readonly IRoadPenetrationService _roadPenetrationService;

// 加载数据并分析
var roads = await _geospatialService.LoadRoadDataFromShapefileAsync("roads.shp");
var devices = await _geospatialService.LoadDeviceLocationsAsync("devices.csv");
var analyses = await _geospatialService.CalculateBatchRoadPenetrationAsync(roads, devices);
```

### 2. 空间查询
```csharp
var queryRequest = new SpatialQueryRequest
{
    QueryRegion = polygon,
    RoadTypes = new List<RoadType> { RoadType.Highway, RoadType.NationalRoad },
    MinCoverageRate = 80.0
};

var filteredRoads = await _geospatialService.SpatialQueryRoadsAsync(queryRequest, roads);
```

## 依赖库

- **NetTopologySuite**: 几何对象处理和空间运算
- **NetTopologySuite.IO.Shapefile**: Shapefile文件读取
- **ProjNet**: 坐标系转换（预留接口）

## 注意事项

1. 确保Shapefile文件完整（.shp, .shx, .dbf文件）
2. 设备CSV文件编码为UTF-8
3. 坐标系统一使用WGS84（EPSG:4326）
4. 大数据量处理时注意内存使用
5. 复杂几何运算可能耗时较长

## 扩展功能

可以进一步扩展的功能：
- 支持更多空间数据格式（GeoJSON、KML等）
- 实现坐标系转换
- 添加空间索引优化
- 支持实时数据更新
- 集成地图可视化
