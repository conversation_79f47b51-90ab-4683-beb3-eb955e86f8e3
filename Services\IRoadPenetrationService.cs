using APIFurion.Models;

namespace APIFurion.Services;

/// <summary>
/// 道路渗透率服务接口
/// </summary>
public interface IRoadPenetrationService
{
    /// <summary>
    /// 获取道路渗透率统计
    /// </summary>
    /// <param name="request">统计请求参数</param>
    /// <returns>渗透率统计结果</returns>
    Task<PenetrationStatisticsResponse> GetPenetrationStatisticsAsync(PenetrationStatisticsRequest request);

    // 初始化栅格数据
    bool InitializeGrid(int districtId, string testPeriod, Shape cityShape, List<int> fileIds);

    // 计算道路渗透率
    PenetrationResult CalculateRoadPenetration(string roadLayerPath, string regionName);
}
