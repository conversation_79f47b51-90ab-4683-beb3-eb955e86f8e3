using APIFurion.Models;

namespace APIFurion.Services;

/// <summary>
/// 道路渗透率服务接口
/// </summary>
public interface IRoadPenetrationService
{
    /// <summary>
    /// 获取道路渗透率统计
    /// </summary>
    /// <param name="request">统计请求参数</param>
    /// <returns>渗透率统计结果</returns>
    Task<PenetrationStatisticsResponse> GetPenetrationStatisticsAsync(PenetrationStatisticsRequest request);

    /// <summary>
    /// 从Shapefile加载并分析道路渗透率
    /// </summary>
    /// <param name="roadLayerPath">道路图层文件路径</param>
    /// <param name="deviceDataSource">设备数据源</param>
    /// <param name="regionName">区域名称</param>
    /// <returns>渗透率分析结果</returns>
    Task<List<RoadPenetrationAnalysis>> AnalyzeRoadPenetrationFromShapefileAsync(string roadLayerPath, string deviceDataSource, string regionName);

    /// <summary>
    /// 计算区域渗透率统计
    /// </summary>
    /// <param name="regionName">区域名称</param>
    /// <param name="roadLayerPath">道路图层文件路径</param>
    /// <param name="deviceDataSource">设备数据源</param>
    /// <returns>区域渗透率统计结果</returns>
    Task<RegionPenetrationStatistics> CalculateRegionStatisticsAsync(string regionName, string roadLayerPath, string deviceDataSource);
}
