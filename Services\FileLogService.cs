using System.Text;

namespace APIFurion.Services;

/// <summary>
/// 文件日志服务实现
/// </summary>
public class FileLogService : IFileLogService
{
    private readonly string _logDirectory;
    private readonly object _lockObject = new object();

    public FileLogService()
    {
        // 在项目根目录下创建Logs文件夹
        _logDirectory = Path.Combine(Directory.GetCurrentDirectory(), "Logs");
        
        // 确保日志目录存在
        if (!Directory.Exists(_logDirectory))
        {
            Directory.CreateDirectory(_logDirectory);
        }
    }

    /// <summary>
    /// 写入访问日志
    /// </summary>
    public void WriteAccessLog(string clientIp, DateTime requestTime, string method, string path, int statusCode, long responseTime)
    {
        var logMessage = $"[{requestTime:yyyy-MM-dd HH:mm:ss}] IP:{clientIp} {method} {path} - 状态码:{statusCode} 响应时间:{responseTime}ms";
        WriteToFile("access", logMessage);
    }

    /// <summary>
    /// 写入通用日志
    /// </summary>
    public void WriteLog(string level, string message, Exception? exception = null)
    {
        var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{level.ToUpper()}] {message}";
        
        if (exception != null)
        {
            logMessage += $"\n异常信息: {exception.Message}\n堆栈跟踪: {exception.StackTrace}";
        }
        
        WriteToFile("application", logMessage);
    }

    /// <summary>
    /// 写入错误日志
    /// </summary>
    public void WriteError(string message, Exception? exception = null)
    {
        WriteLog("ERROR", message, exception);
    }

    /// <summary>
    /// 写入信息日志
    /// </summary>
    public void WriteInfo(string message)
    {
        WriteLog("INFO", message);
    }

    /// <summary>
    /// 写入文件
    /// </summary>
    /// <param name="logType">日志类型</param>
    /// <param name="message">日志消息</param>
    private void WriteToFile(string logType, string message)
    {
        try
        {
            lock (_lockObject)
            {
                var today = DateTime.Now.ToString("yyyy-MM-dd");
                var fileName = $"{logType}_{today}.txt";
                var filePath = Path.Combine(_logDirectory, fileName);

                // 使用UTF-8编码写入文件，确保立即写入磁盘
                using var writer = new StreamWriter(filePath, append: true, Encoding.UTF8);
                writer.WriteLine(message);
                writer.Flush();

                // 确保数据写入磁盘
                if (writer.BaseStream is FileStream fs)
                {
                    fs.Flush(true);
                }
            }
        }
        catch (Exception ex)
        {
            // 如果写入文件失败，输出到控制台
            Console.WriteLine($"写入日志文件失败: {ex.Message}");
            Console.WriteLine($"原始日志: {message}");
        }
    }

    /// <summary>
    /// 获取指定日期的访问日志
    /// </summary>
    /// <param name="date">日期</param>
    /// <returns>日志内容</returns>
    public async Task<string> GetAccessLogAsync(DateTime date)
    {
        var fileName = $"access_{date:yyyy-MM-dd}.txt";
        var filePath = Path.Combine(_logDirectory, fileName);

        if (!File.Exists(filePath))
        {
            return "指定日期的日志文件不存在";
        }

        try
        {
            return await File.ReadAllTextAsync(filePath, Encoding.UTF8);
        }
        catch (Exception ex)
        {
            return $"读取日志文件失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 获取日志文件列表
    /// </summary>
    /// <returns>日志文件列表</returns>
    public List<string> GetLogFiles()
    {
        try
        {
            return Directory.GetFiles(_logDirectory, "*.txt")
                           .Select(Path.GetFileName)
                           .Where(name => name != null)
                           .Cast<string>()
                           .OrderByDescending(name => name)
                           .ToList();
        }
        catch
        {
            return new List<string>();
        }
    }
}
