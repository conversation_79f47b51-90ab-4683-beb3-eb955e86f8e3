using Microsoft.AspNetCore.Mvc;
using APIFurion.Services;

namespace APIFurion.Controllers;

/// <summary>
/// 日志管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class LogController : ControllerBase
{
    private readonly IAccessLogService _accessLogService;
    private readonly IFileLogService _fileLogService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="accessLogService">访问日志服务</param>
    /// <param name="fileLogService">文件日志服务</param>
    public LogController(IAccessLogService accessLogService, IFileLogService fileLogService)
    {
        _accessLogService = accessLogService;
        _fileLogService = fileLogService;
    }

    /// <summary>
    /// 获取访问统计信息
    /// </summary>
    /// <returns>访问统计数据</returns>
    [HttpGet("statistics")]
    public async Task<ActionResult<object>> GetAccessStatistics()
    {
        var statistics = await _accessLogService.GetAccessStatisticsAsync();
        return Ok(statistics);
    }

    /// <summary>
    /// 获取当前请求的IP信息
    /// </summary>
    /// <returns>IP信息</returns>
    [HttpGet("current-ip")]
    public ActionResult<object> GetCurrentIp()
    {
        var remoteIp = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        var forwardedFor = HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        var realIp = HttpContext.Request.Headers["X-Real-IP"].FirstOrDefault();

        var clientIp = !string.IsNullOrEmpty(forwardedFor) ? forwardedFor.Split(',')[0].Trim() :
                       !string.IsNullOrEmpty(realIp) ? realIp :
                       remoteIp;

        return Ok(new
        {
            ClientIp = clientIp,
            RemoteIp = remoteIp,
            ForwardedFor = forwardedFor,
            RealIp = realIp,
            UserAgent = HttpContext.Request.Headers.UserAgent.FirstOrDefault(),
            Timestamp = DateTime.Now
        });
    }

    /// <summary>
    /// 获取日志文件列表
    /// </summary>
    /// <returns>日志文件列表</returns>
    [HttpGet("files")]
    public ActionResult<object> GetLogFiles()
    {
        var fileLogService = _fileLogService as FileLogService;
        var files = fileLogService?.GetLogFiles() ?? new List<string>();

        return Ok(new
        {
            LogDirectory = Path.Combine(Directory.GetCurrentDirectory(), "Logs"),
            Files = files,
            Count = files.Count
        });
    }

    /// <summary>
    /// 获取指定日期的访问日志
    /// </summary>
    /// <param name="date">日期 (格式: yyyy-MM-dd)</param>
    /// <returns>日志内容</returns>
    [HttpGet("access/{date}")]
    public async Task<ActionResult<object>> GetAccessLog(string date)
    {
        if (!DateTime.TryParseExact(date, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var logDate))
        {
            return BadRequest("日期格式错误，请使用 yyyy-MM-dd 格式");
        }

        var fileLogService = _fileLogService as FileLogService;
        var content = await fileLogService!.GetAccessLogAsync(logDate);

        return Ok(new
        {
            Date = date,
            Content = content,
            Lines = content.Split('\n', StringSplitOptions.RemoveEmptyEntries).Length
        });
    }

    /// <summary>
    /// 测试写入日志
    /// </summary>
    /// <param name="message">测试消息</param>
    /// <returns>结果</returns>
    [HttpPost("test")]
    public ActionResult<object> TestLog([FromBody] string message)
    {
        _fileLogService.WriteInfo($"测试日志: {message}");

        return Ok(new
        {
            Message = "日志写入成功",
            TestMessage = message,
            Timestamp = DateTime.Now
        });
    }
}
