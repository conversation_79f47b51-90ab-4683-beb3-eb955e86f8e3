namespace APIFurion.Services;

/// <summary>
/// 访问日志服务接口
/// </summary>
public interface IAccessLogService
{
    /// <summary>
    /// 记录API访问日志
    /// </summary>
    /// <param name="clientIp">客户端IP</param>
    /// <param name="method">HTTP方法</param>
    /// <param name="path">请求路径</param>
    /// <param name="userAgent">用户代理</param>
    /// <param name="statusCode">响应状态码</param>
    /// <param name="responseTime">响应时间(毫秒)</param>
    void LogApiAccess(string clientIp, string method, string path, int statusCode, long responseTime);

    /// <summary>
    /// 获取访问统计信息
    /// </summary>
    /// <returns>访问统计</returns>
    Task<object> GetAccessStatisticsAsync();
}
