using NetTopologySuite.Geometries;

namespace APIFurion.Models;

/// <summary>
/// 道路几何信息模型
/// </summary>
public class RoadGeometry
{
    /// <summary>
    /// 道路ID
    /// </summary>
    public string RoadId { get; set; } = string.Empty;

    /// <summary>
    /// 道路名称
    /// </summary>
    public string RoadName { get; set; } = string.Empty;

    /// <summary>
    /// 道路几何形状（线串）
    /// </summary>
    public LineString Geometry { get; set; } = null!;

    /// <summary>
    /// 道路长度（米）
    /// </summary>
    public double Length { get; set; }

    /// <summary>
    /// 道路类型
    /// </summary>
    public RoadType RoadType { get; set; }

    /// <summary>
    /// 所属区域
    /// </summary>
    public string Region { get; set; } = string.Empty;

    /// <summary>
    /// 道路属性信息
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// 设备位置信息模型
/// </summary>
public class DeviceLocation
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public string DeviceId { get; set; } = string.Empty;

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; } = string.Empty;

    /// <summary>
    /// 设备位置（点）
    /// </summary>
    public Point Location { get; set; } = null!;

    /// <summary>
    /// 设备类型
    /// </summary>
    public DeviceType DeviceType { get; set; }

    /// <summary>
    /// 覆盖半径（米）
    /// </summary>
    public double CoverageRadius { get; set; }

    /// <summary>
    /// 安装时间
    /// </summary>
    public DateTime InstallTime { get; set; }

    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline { get; set; }
}

/// <summary>
/// 设备类型枚举
/// </summary>
public enum DeviceType
{
    /// <summary>
    /// 智能设备
    /// </summary>
    Smart = 1,

    /// <summary>
    /// 传统设备
    /// </summary>
    Traditional = 2
}

/// <summary>
/// 道路渗透率分析结果
/// </summary>
public class RoadPenetrationAnalysis
{
    /// <summary>
    /// 道路ID
    /// </summary>
    public string RoadId { get; set; } = string.Empty;

    /// <summary>
    /// 道路名称
    /// </summary>
    public string RoadName { get; set; } = string.Empty;

    /// <summary>
    /// 道路总长度（米）
    /// </summary>
    public double TotalLength { get; set; }

    /// <summary>
    /// 被覆盖的道路长度（米）
    /// </summary>
    public double CoveredLength { get; set; }

    /// <summary>
    /// 未被覆盖的道路长度（米）
    /// </summary>
    public double UncoveredLength { get; set; }

    /// <summary>
    /// 覆盖率（百分比）
    /// </summary>
    public double CoveragePercentage { get; set; }

    /// <summary>
    /// 重复覆盖长度（米）
    /// </summary>
    public double OverlapLength { get; set; }

    /// <summary>
    /// 智能设备数量
    /// </summary>
    public int SmartDeviceCount { get; set; }

    /// <summary>
    /// 传统设备数量
    /// </summary>
    public int TraditionalDeviceCount { get; set; }

    /// <summary>
    /// 总设备数量
    /// </summary>
    public int TotalDeviceCount => SmartDeviceCount + TraditionalDeviceCount;

    /// <summary>
    /// 智能设备渗透率（百分比）
    /// </summary>
    public double SmartDevicePenetrationRate => TotalDeviceCount > 0 ? (double)SmartDeviceCount / TotalDeviceCount * 100 : 0;

    /// <summary>
    /// 设备密度（设备数/公里）
    /// </summary>
    public double DeviceDensity => TotalLength > 0 ? TotalDeviceCount / (TotalLength / 1000) : 0;

    /// <summary>
    /// 覆盖的道路段
    /// </summary>
    public List<LineString> CoveredSegments { get; set; } = new();

    /// <summary>
    /// 未覆盖的道路段
    /// </summary>
    public List<LineString> UncoveredSegments { get; set; } = new();

    /// <summary>
    /// 分析时间
    /// </summary>
    public DateTime AnalysisTime { get; set; } = DateTime.Now;
}

/// <summary>
/// 区域渗透率统计
/// </summary>
public class RegionPenetrationStatistics
{
    /// <summary>
    /// 区域名称
    /// </summary>
    public string RegionName { get; set; } = string.Empty;

    /// <summary>
    /// 区域边界
    /// </summary>
    public Polygon? RegionBoundary { get; set; }

    /// <summary>
    /// 道路总数
    /// </summary>
    public int TotalRoadCount { get; set; }

    /// <summary>
    /// 道路总长度（公里）
    /// </summary>
    public double TotalRoadLength { get; set; }

    /// <summary>
    /// 平均覆盖率（百分比）
    /// </summary>
    public double AverageCoverageRate { get; set; }

    /// <summary>
    /// 平均智能设备渗透率（百分比）
    /// </summary>
    public double AverageSmartDevicePenetrationRate { get; set; }

    /// <summary>
    /// 总设备数量
    /// </summary>
    public int TotalDeviceCount { get; set; }

    /// <summary>
    /// 智能设备数量
    /// </summary>
    public int SmartDeviceCount { get; set; }

    /// <summary>
    /// 道路渗透率分析列表
    /// </summary>
    public List<RoadPenetrationAnalysis> RoadAnalyses { get; set; } = new();
}

/// <summary>
/// 空间查询参数
/// </summary>
public class SpatialQueryRequest
{
    /// <summary>
    /// 查询区域（可选）
    /// </summary>
    public Polygon? QueryRegion { get; set; }

    /// <summary>
    /// 道路类型过滤（可选）
    /// </summary>
    public List<RoadType>? RoadTypes { get; set; }

    /// <summary>
    /// 设备类型过滤（可选）
    /// </summary>
    public List<DeviceType>? DeviceTypes { get; set; }

    /// <summary>
    /// 最小覆盖率过滤（可选）
    /// </summary>
    public double? MinCoverageRate { get; set; }

    /// <summary>
    /// 最大覆盖率过滤（可选）
    /// </summary>
    public double? MaxCoverageRate { get; set; }

    /// <summary>
    /// 缓冲区距离（米，可选）
    /// </summary>
    public double? BufferDistance { get; set; }
}
