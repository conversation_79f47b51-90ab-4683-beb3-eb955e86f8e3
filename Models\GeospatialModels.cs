using NetTopologySuite.Geometries;

namespace APIFurion.Models;

/// <summary>
/// 道路几何信息模型
/// </summary>
public class RoadGeometry
{
    /// <summary>
    /// 道路ID
    /// </summary>
    public string RoadId { get; set; } = string.Empty;

    /// <summary>
    /// 道路名称
    /// </summary>
    public string RoadName { get; set; } = string.Empty;

    /// <summary>
    /// 道路几何形状（线串）
    /// </summary>
    public LineString Geometry { get; set; } = null!;

    /// <summary>
    /// 道路长度（米）
    /// </summary>
    public double Length { get; set; }

    /// <summary>
    /// 道路类型
    /// </summary>
    public RoadType RoadType { get; set; }

    /// <summary>
    /// 所属区域
    /// </summary>
    public string Region { get; set; } = string.Empty;

    /// <summary>
    /// 道路属性信息
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// 道路渗透率分析结果
/// </summary>
public class RoadPenetrationAnalysis
{
    /// <summary>
    /// 道路ID
    /// </summary>
    public string RoadId { get; set; } = string.Empty;

    /// <summary>
    /// 道路名称
    /// </summary>
    public string RoadName { get; set; } = string.Empty;

    /// <summary>
    /// 道路总长度（米）
    /// </summary>
    public double TotalLength { get; set; }

    /// <summary>
    /// 被覆盖的道路长度（米）
    /// </summary>
    public double CoveredLength { get; set; }

    /// <summary>
    /// 未被覆盖的道路长度（米）
    /// </summary>
    public double UncoveredLength { get; set; }

    /// <summary>
    /// 覆盖率（百分比）
    /// </summary>
    public double CoveragePercentage { get; set; }

    /// <summary>
    /// 重复覆盖长度（米）
    /// </summary>
    public double OverlapLength { get; set; }

    /// <summary>
    /// 智能设备数量
    /// </summary>
    public int SmartDeviceCount { get; set; }

    /// <summary>
    /// 传统设备数量
    /// </summary>
    public int TraditionalDeviceCount { get; set; }

    /// <summary>
    /// 总设备数量
    /// </summary>
    public int TotalDeviceCount => SmartDeviceCount + TraditionalDeviceCount;

    /// <summary>
    /// 智能设备渗透率（百分比）
    /// </summary>
    public double SmartDevicePenetrationRate => TotalDeviceCount > 0 ? (double)SmartDeviceCount / TotalDeviceCount * 100 : 0;

    /// <summary>
    /// 设备密度（设备数/公里）
    /// </summary>
    public double DeviceDensity => TotalLength > 0 ? TotalDeviceCount / (TotalLength / 1000) : 0;

    /// <summary>
    /// 覆盖的道路段
    /// </summary>
    public List<LineString> CoveredSegments { get; set; } = new();

    /// <summary>
    /// 未覆盖的道路段
    /// </summary>
    public List<LineString> UncoveredSegments { get; set; } = new();

    /// <summary>
    /// 分析时间
    /// </summary>
    public DateTime AnalysisTime { get; set; } = DateTime.Now;
}


public class PenetrationResult
{
    public string RoadName { get; set; }
    public string RegionName { get; set; }
    public double CoveredDistance { get; set; }
    public double UncoveredDistance { get; set; }
    public double CoveragePercentage { get; set; }
    public double RepeatedCoverage { get; set; }
}

public class RegionPenetrationSummary
{
    public string RegionName { get; set; }
    public double TotalTestMileage { get; set; }
    public double CoveredMileage { get; set; }
    public double OverallCoverageRate { get; set; }
    public List<PenetrationResult> RoadDetails { get; set; }
}
