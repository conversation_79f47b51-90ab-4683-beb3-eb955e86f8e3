using APIFurion.Models;
using NetTopologySuite.Geometries;

namespace APIFurion.Services;

/// <summary>
/// 地理信息处理服务接口
/// </summary>
public interface IGeospatialService
{
    /// <summary>
    /// 从Shapefile加载道路数据
    /// </summary>
    /// <param name="shapefilePath">Shapefile文件路径</param>
    /// <returns>道路几何信息列表</returns>
    Task<List<RoadGeometry>> LoadRoadDataFromShapefileAsync(string shapefilePath);

    /// <summary>
    /// 从数据源加载设备位置数据
    /// </summary>
    /// <param name="deviceDataSource">设备数据源</param>
    /// <returns>设备位置信息列表</returns>
    Task<List<DeviceLocation>> LoadDeviceLocationsAsync(string deviceDataSource);

    /// <summary>
    /// 计算道路渗透率分析
    /// </summary>
    /// <param name="road">道路几何信息</param>
    /// <param name="devices">设备位置列表</param>
    /// <returns>道路渗透率分析结果</returns>
    Task<RoadPenetrationAnalysis> CalculateRoadPenetrationAsync(RoadGeometry road, List<DeviceLocation> devices);

    /// <summary>
    /// 批量计算多条道路的渗透率分析
    /// </summary>
    /// <param name="roads">道路几何信息列表</param>
    /// <param name="devices">设备位置列表</param>
    /// <returns>道路渗透率分析结果列表</returns>
    Task<List<RoadPenetrationAnalysis>> CalculateBatchRoadPenetrationAsync(List<RoadGeometry> roads, List<DeviceLocation> devices);

    /// <summary>
    /// 计算区域渗透率统计
    /// </summary>
    /// <param name="regionName">区域名称</param>
    /// <param name="regionBoundary">区域边界（可选）</param>
    /// <param name="roads">道路几何信息列表</param>
    /// <param name="devices">设备位置列表</param>
    /// <returns>区域渗透率统计</returns>
    Task<RegionPenetrationStatistics> CalculateRegionPenetrationStatisticsAsync(
        string regionName, 
        Polygon? regionBoundary, 
        List<RoadGeometry> roads, 
        List<DeviceLocation> devices);

    /// <summary>
    /// 空间查询道路
    /// </summary>
    /// <param name="queryRequest">空间查询参数</param>
    /// <param name="roads">道路几何信息列表</param>
    /// <returns>符合条件的道路列表</returns>
    Task<List<RoadGeometry>> SpatialQueryRoadsAsync(SpatialQueryRequest queryRequest, List<RoadGeometry> roads);

    /// <summary>
    /// 空间查询设备
    /// </summary>
    /// <param name="queryRequest">空间查询参数</param>
    /// <param name="devices">设备位置列表</param>
    /// <returns>符合条件的设备列表</returns>
    Task<List<DeviceLocation>> SpatialQueryDevicesAsync(SpatialQueryRequest queryRequest, List<DeviceLocation> devices);

    /// <summary>
    /// 计算道路与设备的空间关系
    /// </summary>
    /// <param name="road">道路几何信息</param>
    /// <param name="device">设备位置信息</param>
    /// <returns>是否在覆盖范围内</returns>
    bool IsDeviceCoveringRoad(RoadGeometry road, DeviceLocation device);

    /// <summary>
    /// 计算道路被设备覆盖的部分
    /// </summary>
    /// <param name="road">道路几何信息</param>
    /// <param name="device">设备位置信息</param>
    /// <returns>被覆盖的道路段</returns>
    LineString? GetCoveredRoadSegment(RoadGeometry road, DeviceLocation device);

    /// <summary>
    /// 计算道路未被覆盖的部分
    /// </summary>
    /// <param name="road">道路几何信息</param>
    /// <param name="coveredSegments">已覆盖的道路段列表</param>
    /// <returns>未覆盖的道路段列表</returns>
    List<LineString> GetUncoveredRoadSegments(RoadGeometry road, List<LineString> coveredSegments);

    /// <summary>
    /// 坐标系转换
    /// </summary>
    /// <param name="geometry">几何对象</param>
    /// <param name="sourceSrid">源坐标系SRID</param>
    /// <param name="targetSrid">目标坐标系SRID</param>
    /// <returns>转换后的几何对象</returns>
    Geometry TransformCoordinateSystem(Geometry geometry, int sourceSrid, int targetSrid);

    /// <summary>
    /// 创建缓冲区
    /// </summary>
    /// <param name="geometry">几何对象</param>
    /// <param name="distance">缓冲区距离（米）</param>
    /// <returns>缓冲区几何对象</returns>
    Geometry CreateBuffer(Geometry geometry, double distance);

    /// <summary>
    /// 计算两个几何对象的距离
    /// </summary>
    /// <param name="geometry1">几何对象1</param>
    /// <param name="geometry2">几何对象2</param>
    /// <returns>距离（米）</returns>
    double CalculateDistance(Geometry geometry1, Geometry geometry2);

    /// <summary>
    /// 判断点是否在多边形内
    /// </summary>
    /// <param name="point">点</param>
    /// <param name="polygon">多边形</param>
    /// <returns>是否在多边形内</returns>
    bool IsPointInPolygon(Point point, Polygon polygon);

    /// <summary>
    /// 计算线串的长度
    /// </summary>
    /// <param name="lineString">线串</param>
    /// <returns>长度（米）</returns>
    double CalculateLineStringLength(LineString lineString);

    /// <summary>
    /// 合并多个几何对象
    /// </summary>
    /// <param name="geometries">几何对象列表</param>
    /// <returns>合并后的几何对象</returns>
    Geometry UnionGeometries(List<Geometry> geometries);

    /// <summary>
    /// 计算几何对象的交集
    /// </summary>
    /// <param name="geometry1">几何对象1</param>
    /// <param name="geometry2">几何对象2</param>
    /// <returns>交集几何对象</returns>
    Geometry? IntersectGeometries(Geometry geometry1, Geometry geometry2);

    /// <summary>
    /// 计算几何对象的差集
    /// </summary>
    /// <param name="geometry1">几何对象1</param>
    /// <param name="geometry2">几何对象2</param>
    /// <returns>差集几何对象</returns>
    Geometry? DifferenceGeometries(Geometry geometry1, Geometry geometry2);
}
