using APIFurion.Models;
using NetTopologySuite.Geometries;

namespace APIFurion.Services;

/// <summary>
/// 地理信息处理服务接口
/// </summary>
public interface IGeospatialService
{
    /// <summary>
    /// 从Shapefile加载道路数据
    /// </summary>
    /// <param name="shapefilePath">Shapefile文件路径</param>
    /// <returns>道路几何信息列表</returns>
    Task<List<RoadGeometry>> LoadRoadDataFromShapefileAsync(string shapefilePath);

    /// <summary>
    /// 从Shapefile加载道路数据（指定编码）
    /// </summary>
    /// <param name="shapefilePath">Shapefile文件路径</param>
    /// <param name="encoding">指定的编码，如果为null则自动检测</param>
    /// <returns>道路几何信息列表</returns>
    Task<List<RoadGeometry>> LoadRoadDataFromShapefileAsync(string shapefilePath, System.Text.Encoding? encoding);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="roads"></param>
    /// <returns></returns>
    Task<List<PenetrationResult>> CalculateBatchRoadPenetrationAsync(List<RoadGeometry> roads);

    /// <summary>
    /// 坐标系转换
    /// </summary>
    /// <param name="geometry">几何对象</param>
    /// <param name="sourceSrid">源坐标系SRID</param>
    /// <param name="targetSrid">目标坐标系SRID</param>
    /// <returns>转换后的几何对象</returns>
    Geometry TransformCoordinateSystem(Geometry geometry, int sourceSrid, int targetSrid);

    /// <summary>
    /// 创建缓冲区
    /// </summary>
    /// <param name="geometry">几何对象</param>
    /// <param name="distance">缓冲区距离（米）</param>
    /// <returns>缓冲区几何对象</returns>
    Geometry CreateBuffer(Geometry geometry, double distance);

    /// <summary>
    /// 计算两个几何对象的距离
    /// </summary>
    /// <param name="geometry1">几何对象1</param>
    /// <param name="geometry2">几何对象2</param>
    /// <returns>距离（米）</returns>
    double CalculateDistance(Geometry geometry1, Geometry geometry2);

    /// <summary>
    /// 判断点是否在多边形内
    /// </summary>
    /// <param name="point">点</param>
    /// <param name="polygon">多边形</param>
    /// <returns>是否在多边形内</returns>
    bool IsPointInPolygon(Point point, Polygon polygon);

    /// <summary>
    /// 计算线串的长度
    /// </summary>
    /// <param name="lineString">线串</param>
    /// <returns>长度（米）</returns>
    double CalculateLineStringLength(LineString lineString);

    /// <summary>
    /// 合并多个几何对象
    /// </summary>
    /// <param name="geometries">几何对象列表</param>
    /// <returns>合并后的几何对象</returns>
    Geometry UnionGeometries(List<Geometry> geometries);

    /// <summary>
    /// 计算几何对象的交集
    /// </summary>
    /// <param name="geometry1">几何对象1</param>
    /// <param name="geometry2">几何对象2</param>
    /// <returns>交集几何对象</returns>
    Geometry? IntersectGeometries(Geometry geometry1, Geometry geometry2);

    /// <summary>
    /// 计算几何对象的差集
    /// </summary>
    /// <param name="geometry1">几何对象1</param>
    /// <param name="geometry2">几何对象2</param>
    /// <returns>差集几何对象</returns>
    Geometry? DifferenceGeometries(Geometry geometry1, Geometry geometry2);
}
