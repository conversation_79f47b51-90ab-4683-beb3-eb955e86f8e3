using APIFurion.Models;
using Furion.DependencyInjection;

namespace APIFurion.Services;

/// <summary>
/// 道路渗透率服务实现
/// </summary>
public class RoadPenetrationService : IRoadPenetrationService, IScoped
{
    private readonly IFileLogService _fileLogService;
    private readonly IGeospatialService _geospatialService;
    private static readonly List<RoadPenetrationModel> _roadPenetrationData = new();

    public RoadPenetrationService(IFileLogService fileLogService, IGeospatialService geospatialService)
    {
        _fileLogService = fileLogService;
        _geospatialService = geospatialService;
    }

    /// <summary>
    /// 获取道路渗透率统计
    /// </summary>
    public async Task<PenetrationStatisticsResponse> GetPenetrationStatisticsAsync(PenetrationStatisticsRequest request)
    {
        await Task.CompletedTask; // 模拟异步操作

        _fileLogService.WriteInfo($"开始获取道路渗透率统计 - 请求参数: {System.Text.Json.JsonSerializer.Serialize(request)}");

        var query = _roadPenetrationData.AsQueryable();

        // 按条件筛选
        if (request.StartDate.HasValue)
        {
            query = query.Where(x => x.StatisticsTime >= request.StartDate.Value);
        }

        if (request.EndDate.HasValue)
        {
            query = query.Where(x => x.StatisticsTime <= request.EndDate.Value);
        }

        if (request.RoadType.HasValue)
        {
            query = query.Where(x => x.RoadType == request.RoadType.Value);
        }

        if (!string.IsNullOrEmpty(request.Region))
        {
            query = query.Where(x => x.Region.Contains(request.Region));
        }

        var totalCount = query.Count();
        var roads = query
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToList();

        var response = new PenetrationStatisticsResponse
        {
            Roads = roads,
            TotalCount = totalCount,
            AveragePenetrationRate = roads.Any() ? roads.Average(x => x.PenetrationRate) : 0,
            MaxPenetrationRate = roads.Any() ? roads.Max(x => x.PenetrationRate) : 0,
            MinPenetrationRate = roads.Any() ? roads.Min(x => x.PenetrationRate) : 0,
            TotalSmartDevices = roads.Sum(x => x.SmartDeviceCount),
            TotalDevices = roads.Sum(x => x.TotalDeviceCount)
        };

        _fileLogService.WriteInfo($"道路渗透率统计完成 - 返回{roads.Count}条记录");

        return response;
    }

    /// <summary>
    /// 从Shapefile加载并分析道路渗透率
    /// </summary>
    public async Task<List<RoadPenetrationAnalysis>> AnalyzeRoadPenetrationFromShapefileAsync(string roadLayerPath, string deviceDataSource, string regionName)
    {
        try
        {
            _fileLogService.WriteInfo($"开始分析道路渗透率 - 道路图层: {roadLayerPath}, 设备数据: {deviceDataSource}, 区域: {regionName}");

            // 加载道路数据
            var roads = await _geospatialService.LoadRoadDataFromShapefileAsync(roadLayerPath);
            if (!roads.Any())
            {
                _fileLogService.WriteWarning("未找到道路数据");
                return new List<RoadPenetrationAnalysis>();
            }

            // 加载设备数据
            var devices = await _geospatialService.LoadDeviceLocationsAsync(deviceDataSource);
            if (!devices.Any())
            {
                _fileLogService.WriteWarning("未找到设备数据");
                return new List<RoadPenetrationAnalysis>();
            }

            // 按区域过滤道路（如果指定了区域）
            if (!string.IsNullOrEmpty(regionName))
            {
                roads = roads.Where(r => r.Region.Equals(regionName, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            // 批量计算道路渗透率分析
            var analyses = await _geospatialService.CalculateBatchRoadPenetrationAsync(roads, devices);

            _fileLogService.WriteInfo($"完成道路渗透率分析 - 共分析{analyses.Count}条道路");

            return analyses;
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"分析道路渗透率时发生错误: {ex.Message}");
            return new List<RoadPenetrationAnalysis>();
        }
    }

    /// <summary>
    /// 计算区域渗透率统计
    /// </summary>
    public async Task<RegionPenetrationStatistics> CalculateRegionStatisticsAsync(string regionName, string roadLayerPath, string deviceDataSource)
    {
        try
        {
            _fileLogService.WriteInfo($"开始计算区域渗透率统计 - 区域: {regionName}");

            // 加载道路数据
            var roads = await _geospatialService.LoadRoadDataFromShapefileAsync(roadLayerPath);

            // 加载设备数据
            var devices = await _geospatialService.LoadDeviceLocationsAsync(deviceDataSource);

            // 计算区域统计
            var statistics = await _geospatialService.CalculateRegionPenetrationStatisticsAsync(regionName, null, roads, devices);

            _fileLogService.WriteInfo($"完成区域渗透率统计 - 区域: {regionName}, 道路数: {statistics.TotalRoadCount}, 设备数: {statistics.TotalDeviceCount}");

            return statistics;
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"计算区域渗透率统计时发生错误: {ex.Message}");
            return new RegionPenetrationStatistics { RegionName = regionName };
        }
    }
}
