using APIFurion.Models;

namespace APIFurion.Services;

/// <summary>
/// 道路渗透率服务实现
/// </summary>
public class RoadPenetrationService : IRoadPenetrationService
{
    private readonly IFileLogService _fileLogService;
    private static readonly List<RoadPenetrationModel> _roadPenetrationData = new();

    public RoadPenetrationService(IFileLogService fileLogService)
    {
        _fileLogService = fileLogService;
        

    }

    /// <summary>
    /// 获取道路渗透率统计
    /// </summary>
    public async Task<PenetrationStatisticsResponse> GetPenetrationStatisticsAsync(PenetrationStatisticsRequest request)
    {
        await Task.CompletedTask; // 模拟异步操作

        _fileLogService.WriteInfo($"开始获取道路渗透率统计 - 请求参数: {System.Text.Json.JsonSerializer.Serialize(request)}");

        var query = _roadPenetrationData.AsQueryable();

        // 按条件筛选
        if (request.StartDate.HasValue)
        {
            query = query.Where(x => x.StatisticsTime >= request.StartDate.Value);
        }

        if (request.EndDate.HasValue)
        {
            query = query.Where(x => x.StatisticsTime <= request.EndDate.Value);
        }

        if (request.RoadType.HasValue)
        {
            query = query.Where(x => x.RoadType == request.RoadType.Value);
        }

        if (!string.IsNullOrEmpty(request.Region))
        {
            query = query.Where(x => x.Region.Contains(request.Region));
        }

        var totalCount = query.Count();
        var roads = query
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToList();

        var response = new PenetrationStatisticsResponse
        {
            Roads = roads,
            TotalCount = totalCount,
            AveragePenetrationRate = roads.Any() ? roads.Average(x => x.PenetrationRate) : 0,
            MaxPenetrationRate = roads.Any() ? roads.Max(x => x.PenetrationRate) : 0,
            MinPenetrationRate = roads.Any() ? roads.Min(x => x.PenetrationRate) : 0,
            TotalSmartDevices = roads.Sum(x => x.SmartDeviceCount),
            TotalDevices = roads.Sum(x => x.TotalDeviceCount)
        };

        _fileLogService.WriteInfo($"道路渗透率统计完成 - 返回{roads.Count}条记录");

        return response;
    }




}
