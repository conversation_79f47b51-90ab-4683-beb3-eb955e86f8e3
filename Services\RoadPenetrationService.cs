using APIFurion.Models;
using Furion.DependencyInjection;

namespace APIFurion.Services;

/// <summary>
/// 道路渗透率服务实现
/// </summary>
public class RoadPenetrationService : IRoadPenetrationService, IScoped
{
    private readonly IFileLogService _fileLogService;
    private readonly IGeospatialService _geospatialService;
    private static readonly List<RoadPenetrationModel> _roadPenetrationData = new();

    public RoadPenetrationService(IFileLogService fileLogService, IGeospatialService geospatialService)
    {
        _fileLogService = fileLogService;
        _geospatialService = geospatialService;
    }


    /// <summary>
    /// 从Shapefile加载并分析道路渗透率
    /// </summary>
    public async Task<List<PenetrationResult>> AnalyzeRoadPenetrationFromShapefileAsync(FileRoadDataRequest request)
    {
        try
        {
            _fileLogService.WriteInfo($"开始分析道路渗透率 - 道路图层: {request.ShapefilePath}");

            // 加载道路数据
            var roads = await _geospatialService.LoadRoadDataFromShapefileAsync(request.ShapefilePath);
            if (!roads.Any())
            {
                _fileLogService.WriteWarning("未找到道路数据");
                return new List<PenetrationResult>();
            }


            // 批量计算道路渗透率分析
            var analyses = await _geospatialService.CalculateBatchRoadPenetrationAsync(roads);

            _fileLogService.WriteInfo($"完成道路渗透率分析 - 共分析{analyses.Count}条道路");

            return analyses;
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"分析道路渗透率时发生错误: {ex.Message}");
            return new List<PenetrationResult>();
        }
    }


}
