var builder = WebApplication.CreateBuilder(args);

// 添加服务到容器
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 配置选项
builder.Services.Configure<APIFurion.Options.WeatherSettings>(
    builder.Configuration.GetSection("WeatherSettings"));
builder.Services.Configure<APIFurion.Options.AppSettings>(
    builder.Configuration.GetSection("AppSettings"));

// 手动注册服务（替代Furion的自动注册）
builder.Services.AddScoped<APIFurion.Services.IWeatherService, APIFurion.Services.WeatherService>();
builder.Services.AddSingleton<APIFurion.Services.IAccessLogService, APIFurion.Services.AccessLogService>();
builder.Services.AddSingleton<APIFurion.Services.IFileLogService, APIFurion.Services.FileLogService>();
builder.Services.AddScoped<APIFurion.Services.IGeospatialService, APIFurion.Services.GeospatialService>();
builder.Services.AddScoped<APIFurion.Services.IRoadPenetrationService, APIFurion.Services.RoadPenetrationService>();

var app = builder.Build();

// 添加IP访问日志中间件
app.Use(async (context, next) =>
{
    var stopwatch = System.Diagnostics.Stopwatch.StartNew();
    var requestTime = DateTime.Now;

    var remoteIp = context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
    var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();

    // 优先使用代理头中的真实IP
    var clientIp = !string.IsNullOrEmpty(forwardedFor) ? forwardedFor.Split(',')[0].Trim() :
                   !string.IsNullOrEmpty(realIp) ? realIp :
                   remoteIp;

    var method = context.Request.Method;
    var path = context.Request.Path;
    //   var userAgent = context.Request.Headers.UserAgent.FirstOrDefault() ?? "Unknown";

    await next();

    stopwatch.Stop();

    // 记录到内存统计
    var accessLogService = context.RequestServices.GetRequiredService<APIFurion.Services.IAccessLogService>();
    accessLogService.LogApiAccess(clientIp, method, path, context.Response.StatusCode, stopwatch.ElapsedMilliseconds);

    // 记录到文件日志 
    var fileLogService = context.RequestServices.GetRequiredService<APIFurion.Services.IFileLogService>();
    fileLogService.WriteAccessLog(clientIp, requestTime, method, path, context.Response.StatusCode, stopwatch.ElapsedMilliseconds);
});

// 配置HTTP请求管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseAuthorization();
app.MapControllers();

app.Run();


