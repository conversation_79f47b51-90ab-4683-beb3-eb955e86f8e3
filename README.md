# APIFurion - 基于Furion框架的.NET Core WebAPI项目

这是一个使用Furion框架搭建的.NET Core WebAPI项目示例，展示了Furion框架的核心功能和最佳实践。

## 项目特性

- ✅ **Furion框架集成** - 使用最新版本的Furion框架
- ✅ **依赖注入** - 自动依赖注入配置
- ✅ **配置管理** - 强类型配置选项
- ✅ **Swagger文档** - 自动生成API文档
- ✅ **异步编程** - 全面支持异步操作
- ✅ **RESTful API** - 标准的REST API设计
- ✅ **访问日志** - 记录每次API调用的IP、响应时间等信息
- ✅ **统计分析** - 提供访问统计和分析功能
- ✅ **道路渗透率统计** - 完整的道路智能设备渗透率统计业务
- ✅ **文件日志系统** - 按天分割的txt日志文件记录

## 技术栈

- .NET 8.0
- Furion *********
- ASP.NET Core WebAPI
- Swagger/OpenAPI

## 项目结构

```
APIFurion/
├── Controllers/           # 控制器
│   ├── WeatherController.cs
│   └── InfoController.cs
├── Services/             # 服务层
│   ├── IWeatherService.cs
│   └── WeatherService.cs
├── Options/              # 配置选项
│   └── WeatherSettings.cs
├── Properties/
│   └── launchSettings.json
├── appsettings.json      # 配置文件
├── Program.cs            # 程序入口
└── APIFurion.csproj      # 项目文件
```

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd APIFurion
```

### 2. 还原依赖
```bash
dotnet restore
```

### 3. 构建项目
```bash
dotnet build
```

### 4. 运行项目
```bash
dotnet run
```

项目将在 `http://localhost:5089` 启动。

### 5. 访问Swagger文档
打开浏览器访问：`http://localhost:5089/swagger`

## API端点

### 天气预报API

- **GET** `/api/weather` - 获取天气预报列表
  - 查询参数：`days` (可选，默认5天，最大30天)

- **GET** `/api/weather/{id}` - 根据ID获取天气预报

### 信息API

- **GET** `/api/info` - 获取API信息
- **GET** `/api/info/health` - 健康检查

### 日志管理API

- **GET** `/api/log/statistics` - 获取访问统计信息
- **GET** `/api/log/current-ip` - 获取当前请求的IP信息

### 道路渗透率统计API

- **POST** `/api/roadpenetration/statistics` - 获取道路渗透率统计
- **GET** `/api/roadpenetration/{roadId}` - 根据道路ID获取渗透率详情
- **POST** `/api/roadpenetration` - 添加道路渗透率数据
- **PUT** `/api/roadpenetration/{roadId}` - 更新道路渗透率数据
- **DELETE** `/api/roadpenetration/{roadId}` - 删除道路渗透率数据
- **GET** `/api/roadpenetration/region/{region}/summary` - 获取区域渗透率汇总
- **GET** `/api/roadpenetration/roadtype/{roadType}/summary` - 获取道路类型渗透率汇总
- **POST** `/api/roadpenetration/batch-import` - 批量导入道路渗透率数据
- **GET** `/api/roadpenetration/road-types` - 获取道路类型枚举列表

## 配置说明

项目使用 `appsettings.json` 进行配置：

```json
{
  "WeatherSettings": {
    "DefaultDays": 5,
    "MaxDays": 30,
    "ApiName": "Furion Weather API",
    "Version": "1.0.0"
  }
}
```

## Furion框架特性演示

### 1. 依赖注入
```csharp
// 服务自动注册
public class WeatherService : IWeatherService, ITransient
{
    // 实现代码
}
```

### 2. 配置选项
```csharp
// 强类型配置
public class WeatherSettings : IConfigurableOptions
{
    public int DefaultDays { get; set; } = 5;
    // 其他配置属性
}
```

### 3. 控制器注入
```csharp
public class WeatherController : ControllerBase
{
    private readonly IWeatherService _weatherService;
    
    public WeatherController(IWeatherService weatherService)
    {
        _weatherService = weatherService;
    }
}
```

## 开发环境

- Visual Studio 2022 或 Visual Studio Code
- .NET 8.0 SDK
- Windows/Linux/macOS

## 许可证

MIT License
