{"openapi": "3.0.4", "info": {"title": "APIFurion", "version": "1.0"}, "paths": {"/api/Info": {"get": {"tags": ["Info"], "responses": {"200": {"description": "OK"}}}}, "/api/Info/health": {"get": {"tags": ["Info"], "responses": {"200": {"description": "OK"}}}}, "/api/Log/statistics": {"get": {"tags": ["Log"], "responses": {"200": {"description": "OK"}}}}, "/api/Log/current-ip": {"get": {"tags": ["Log"], "responses": {"200": {"description": "OK"}}}}, "/api/Log/files": {"get": {"tags": ["Log"], "responses": {"200": {"description": "OK"}}}}, "/api/Log/access/{date}": {"get": {"tags": ["Log"], "parameters": [{"name": "date", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Log/test": {"post": {"tags": ["Log"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK"}}}}, "/RoadPenetration/GetPenetrationStatistics": {"post": {"tags": ["RoadPenetration"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PenetrationStatisticsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PenetrationStatisticsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PenetrationStatisticsRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Weather": {"get": {"tags": ["Weather"], "parameters": [{"name": "days", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 5}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}, "/api/Weather/{id}": {"get": {"tags": ["Weather"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WeatherForecast"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WeatherForecast"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}, "components": {"schemas": {"PenetrationStatisticsRequest": {"type": "object", "properties": {"startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "roadType": {"$ref": "#/components/schemas/RoadType"}, "region": {"type": "string", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "RoadType": {"enum": [1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}