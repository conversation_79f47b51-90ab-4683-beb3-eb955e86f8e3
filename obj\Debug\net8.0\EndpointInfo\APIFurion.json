{"openapi": "3.0.4", "info": {"title": "APIFurion", "version": "1.0"}, "paths": {"/api/Geospatial/analyze-road-penetration": {"post": {"tags": ["Geospatial"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoadPenetrationAnalysisRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoadPenetrationAnalysisRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoadPenetrationAnalysisRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Geospatial/region-statistics": {"post": {"tags": ["Geospatial"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegionStatisticsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegionStatisticsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegionStatisticsRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Geospatial/GetRoad": {"post": {"tags": ["Geospatial"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoadRoadDataRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoadRoadDataRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoadRoadDataRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Geospatial/load-devices": {"post": {"tags": ["Geospatial"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoadDeviceDataRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoadDeviceDataRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoadDeviceDataRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Geospatial/test-encoding": {"post": {"tags": ["Geospatial"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestEncodingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TestEncodingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TestEncodingRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Info": {"get": {"tags": ["Info"], "responses": {"200": {"description": "OK"}}}}, "/api/Info/health": {"get": {"tags": ["Info"], "responses": {"200": {"description": "OK"}}}}, "/api/Log/statistics": {"get": {"tags": ["Log"], "responses": {"200": {"description": "OK"}}}}, "/api/Log/current-ip": {"get": {"tags": ["Log"], "responses": {"200": {"description": "OK"}}}}, "/api/Log/files": {"get": {"tags": ["Log"], "responses": {"200": {"description": "OK"}}}}, "/api/Log/access/{date}": {"get": {"tags": ["Log"], "parameters": [{"name": "date", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Log/test": {"post": {"tags": ["Log"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK"}}}}, "/RoadPenetration/GetPenetrationStatistics": {"post": {"tags": ["RoadPenetration"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PenetrationStatisticsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PenetrationStatisticsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PenetrationStatisticsRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Weather": {"get": {"tags": ["Weather"], "parameters": [{"name": "days", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 5}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}, "/api/Weather/{id}": {"get": {"tags": ["Weather"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WeatherForecast"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WeatherForecast"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}, "components": {"schemas": {"DeviceType": {"enum": [1, 2], "type": "integer", "format": "int32"}, "LoadDeviceDataRequest": {"type": "object", "properties": {"deviceDataSource": {"type": "string", "nullable": true}, "deviceTypeFilter": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceType"}, "nullable": true}, "onlineOnly": {"type": "boolean"}, "minCoverageRadius": {"type": "number", "format": "double", "nullable": true}, "maxCoverageRadius": {"type": "number", "format": "double", "nullable": true}, "installTimeStart": {"type": "string", "format": "date-time", "nullable": true}, "installTimeEnd": {"type": "string", "format": "date-time", "nullable": true}, "maxRecords": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "LoadRoadDataRequest": {"type": "object", "properties": {"shapefilePath": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PenetrationStatisticsRequest": {"type": "object", "properties": {"startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "roadType": {"$ref": "#/components/schemas/RoadType"}, "region": {"type": "string", "nullable": true}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "RegionStatisticsRequest": {"type": "object", "properties": {"regionName": {"type": "string", "nullable": true}, "roadLayerPath": {"type": "string", "nullable": true}, "deviceDataSource": {"type": "string", "nullable": true}, "roadTypes": {"type": "array", "items": {"$ref": "#/components/schemas/RoadType"}, "nullable": true}, "deviceTypes": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceType"}, "nullable": true}, "includeDetailedAnalysis": {"type": "boolean"}}, "additionalProperties": false}, "RoadPenetrationAnalysisRequest": {"type": "object", "properties": {"roadLayerPath": {"type": "string", "nullable": true}, "deviceDataSource": {"type": "string", "nullable": true}, "regionName": {"type": "string", "nullable": true}, "roadTypes": {"type": "array", "items": {"$ref": "#/components/schemas/RoadType"}, "nullable": true}, "deviceTypes": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceType"}, "nullable": true}, "minCoverageRate": {"type": "number", "format": "double", "nullable": true}, "maxCoverageRate": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "RoadType": {"enum": [1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "TestEncodingRequest": {"type": "object", "properties": {"shapefilePath": {"type": "string", "nullable": true}, "maxRecords": {"type": "integer", "format": "int32", "nullable": true}, "detailedOutput": {"type": "boolean"}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}