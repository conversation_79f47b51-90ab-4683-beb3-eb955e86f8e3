using Microsoft.AspNetCore.Mvc;
using APIFurion.Services;

namespace APIFurion.Controllers;

/// <summary>
/// 天气预报控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class WeatherController : ControllerBase
{
    private readonly IWeatherService _weatherService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="weatherService">天气服务</param>
    public WeatherController(IWeatherService weatherService)
    {
        _weatherService = weatherService;
    }

    /// <summary>
    /// 获取天气预报
    /// </summary>
    /// <param name="days">天数，默认5天</param>
    /// <returns>天气预报列表</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<WeatherForecast>>> Get([FromQuery] int days = 5)
    {
        if (days < 1 || days > 30)
        {
            return BadRequest("天数必须在1-30之间");
        }

        var forecasts = await _weatherService.GetWeatherForecastAsync(days);
        return Ok(forecasts);
    }

    /// <summary>
    /// 根据ID获取天气预报
    /// </summary>
    /// <param name="id">天气预报ID</param>
    /// <returns>天气预报</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<WeatherForecast>> GetById(int id)
    {
        var forecast = await _weatherService.GetWeatherForecastByIdAsync(id);

        if (forecast == null)
        {
            return NotFound($"未找到ID为{id}的天气预报");
        }

        return Ok(forecast);
    }
}

/// <summary>
/// 天气预报模型
/// </summary>
public class WeatherForecast
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateOnly Date { get; set; }

    /// <summary>
    /// 摄氏温度
    /// </summary>
    public int TemperatureC { get; set; }

    /// <summary>
    /// 华氏温度
    /// </summary>
    public int TemperatureF => 32 + (int)(TemperatureC / 0.5556);

    /// <summary>
    /// 天气描述
    /// </summary>
    public string? Summary { get; set; }
}
