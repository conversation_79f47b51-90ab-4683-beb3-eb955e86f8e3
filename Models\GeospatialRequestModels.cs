namespace APIFurion.Models;

/// <summary>
/// 道路渗透率分析请求模型
/// </summary>
public class RoadPenetrationAnalysisRequest
{
    /// <summary>
    /// 道路图层文件路径
    /// </summary>
    public string RoadLayerPath { get; set; } = string.Empty;

    /// <summary>
    /// 设备数据源路径
    /// </summary>
    public string DeviceDataSource { get; set; } = string.Empty;

    /// <summary>
    /// 区域名称（可选）
    /// </summary>
    public string? RegionName { get; set; }

    /// <summary>
    /// 道路类型过滤（可选）
    /// </summary>
    public List<RoadType>? RoadTypes { get; set; }

    /// <summary>
    /// 最小覆盖率过滤（可选）
    /// </summary>
    public double? MinCoverageRate { get; set; }

    /// <summary>
    /// 最大覆盖率过滤（可选）
    /// </summary>
    public double? MaxCoverageRate { get; set; }
}

/// <summary>
/// 加载道路数据请求模型
/// </summary>
public class LoadRoadDataRequest
{
    /// <summary>
    /// Shapefile文件路径
    /// </summary>
    public string ShapefilePath { get; set; } = string.Empty;

    ///// <summary>
    ///// 区域名称过滤（可选）
    ///// </summary>
    //public string? RegionFilter { get; set; }

    ///// <summary>
    ///// 道路类型过滤（可选）
    ///// </summary>
    //public List<RoadType>? RoadTypeFilter { get; set; }

    ///// <summary>
    ///// 是否包含几何信息
    ///// </summary>
    //public bool IncludeGeometry { get; set; } = false;

    ///// <summary>
    ///// 最大返回记录数（可选）
    ///// </summary>
    //public int? MaxRecords { get; set; }
}

/// <summary>
/// 文件道路渗透率计算请求模型
/// </summary>
public class FileRoadDataRequest
{
    /// <summary>
    /// Shapefile文件路径
    /// </summary>
    public string ShapefilePath { get; set; } = string.Empty;

    /// <summary>
    /// 文件名称
    /// </summary>
    public string? FileName { get; set; }
}


