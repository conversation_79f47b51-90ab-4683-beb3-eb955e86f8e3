namespace APIFurion.Models;

/// <summary>
/// 道路渗透率分析请求模型
/// </summary>
public class RoadPenetrationAnalysisRequest
{
    /// <summary>
    /// 道路图层文件路径
    /// </summary>
    public string RoadLayerPath { get; set; } = string.Empty;

    /// <summary>
    /// 设备数据源路径
    /// </summary>
    public string DeviceDataSource { get; set; } = string.Empty;

    /// <summary>
    /// 区域名称（可选）
    /// </summary>
    public string? RegionName { get; set; }

    /// <summary>
    /// 道路类型过滤（可选）
    /// </summary>
    public List<RoadType>? RoadTypes { get; set; }

    /// <summary>
    /// 设备类型过滤（可选）
    /// </summary>
    public List<DeviceType>? DeviceTypes { get; set; }

    /// <summary>
    /// 最小覆盖率过滤（可选）
    /// </summary>
    public double? MinCoverageRate { get; set; }

    /// <summary>
    /// 最大覆盖率过滤（可选）
    /// </summary>
    public double? MaxCoverageRate { get; set; }
}

/// <summary>
/// 区域统计请求模型
/// </summary>
public class RegionStatisticsRequest
{
    /// <summary>
    /// 区域名称
    /// </summary>
    public string RegionName { get; set; } = string.Empty;

    /// <summary>
    /// 道路图层文件路径（可选，使用配置文件中的默认值）
    /// </summary>
    public string? RoadLayerPath { get; set; }

    /// <summary>
    /// 设备数据源路径（可选）
    /// </summary>
    public string? DeviceDataSource { get; set; }

    /// <summary>
    /// 道路类型过滤（可选）
    /// </summary>
    public List<RoadType>? RoadTypes { get; set; }

    /// <summary>
    /// 设备类型过滤（可选）
    /// </summary>
    public List<DeviceType>? DeviceTypes { get; set; }

    /// <summary>
    /// 是否包含详细的道路分析结果
    /// </summary>
    public bool IncludeDetailedAnalysis { get; set; } = false;
}

/// <summary>
/// 加载道路数据请求模型
/// </summary>
public class LoadRoadDataRequest
{
    /// <summary>
    /// Shapefile文件路径
    /// </summary>
    public string ShapefilePath { get; set; } = string.Empty;

    ///// <summary>
    ///// 区域名称过滤（可选）
    ///// </summary>
    //public string? RegionFilter { get; set; }

    ///// <summary>
    ///// 道路类型过滤（可选）
    ///// </summary>
    //public List<RoadType>? RoadTypeFilter { get; set; }

    ///// <summary>
    ///// 是否包含几何信息
    ///// </summary>
    //public bool IncludeGeometry { get; set; } = false;

    ///// <summary>
    ///// 最大返回记录数（可选）
    ///// </summary>
    //public int? MaxRecords { get; set; }
}

/// <summary>
/// 加载设备数据请求模型
/// </summary>
public class LoadDeviceDataRequest
{
    /// <summary>
    /// 设备数据源路径
    /// </summary>
    public string DeviceDataSource { get; set; } = string.Empty;

    /// <summary>
    /// 设备类型过滤（可选）
    /// </summary>
    public List<DeviceType>? DeviceTypeFilter { get; set; }

    /// <summary>
    /// 是否只返回在线设备
    /// </summary>
    public bool OnlineOnly { get; set; } = false;

    /// <summary>
    /// 最小覆盖半径过滤（可选）
    /// </summary>
    public double? MinCoverageRadius { get; set; }

    /// <summary>
    /// 最大覆盖半径过滤（可选）
    /// </summary>
    public double? MaxCoverageRadius { get; set; }

    /// <summary>
    /// 安装时间范围过滤 - 开始时间（可选）
    /// </summary>
    public DateTime? InstallTimeStart { get; set; }

    /// <summary>
    /// 安装时间范围过滤 - 结束时间（可选）
    /// </summary>
    public DateTime? InstallTimeEnd { get; set; }

    /// <summary>
    /// 最大返回记录数（可选）
    /// </summary>
    public int? MaxRecords { get; set; }
}

/// <summary>
/// 空间查询请求模型
/// </summary>
public class SpatialQueryRequestModel
{
    /// <summary>
    /// 查询区域的WKT格式字符串（可选）
    /// </summary>
    public string? QueryRegionWkt { get; set; }

    /// <summary>
    /// 查询中心点经度（可选）
    /// </summary>
    public double? CenterLongitude { get; set; }

    /// <summary>
    /// 查询中心点纬度（可选）
    /// </summary>
    public double? CenterLatitude { get; set; }

    /// <summary>
    /// 查询半径（米，可选）
    /// </summary>
    public double? QueryRadius { get; set; }

    /// <summary>
    /// 道路类型过滤（可选）
    /// </summary>
    public List<RoadType>? RoadTypes { get; set; }

    /// <summary>
    /// 设备类型过滤（可选）
    /// </summary>
    public List<DeviceType>? DeviceTypes { get; set; }

    /// <summary>
    /// 最小覆盖率过滤（可选）
    /// </summary>
    public double? MinCoverageRate { get; set; }

    /// <summary>
    /// 最大覆盖率过滤（可选）
    /// </summary>
    public double? MaxCoverageRate { get; set; }

    /// <summary>
    /// 缓冲区距离（米，可选）
    /// </summary>
    public double? BufferDistance { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 20;
}

/// <summary>
/// 批量分析请求模型
/// </summary>
public class BatchAnalysisRequest
{
    /// <summary>
    /// 道路图层文件路径列表
    /// </summary>
    public List<string> RoadLayerPaths { get; set; } = new();

    /// <summary>
    /// 设备数据源路径
    /// </summary>
    public string DeviceDataSource { get; set; } = string.Empty;

    /// <summary>
    /// 区域名称列表（可选）
    /// </summary>
    public List<string>? RegionNames { get; set; }

    /// <summary>
    /// 是否并行处理
    /// </summary>
    public bool ParallelProcessing { get; set; } = true;

    /// <summary>
    /// 最大并行度（可选）
    /// </summary>
    public int? MaxDegreeOfParallelism { get; set; }

    /// <summary>
    /// 是否生成汇总报告
    /// </summary>
    public bool GenerateSummaryReport { get; set; } = true;
}
