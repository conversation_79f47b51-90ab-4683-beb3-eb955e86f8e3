using APIFurion.Models;
using NetTopologySuite.Geometries;
using NetTopologySuite.IO;
using NetTopologySuite.Operation.Buffer;
using NetTopologySuite.Operation.Distance;
using System.Text;

namespace APIFurion.Services;

/// <summary>
/// 地理信息处理服务实现
/// </summary>
public class GeospatialService : IGeospatialService
{
    private readonly IFileLogService _fileLogService;
    private readonly GeometryFactory _geometryFactory;

    public GeospatialService(IFileLogService fileLogService)
    {
        _fileLogService = fileLogService;
        _geometryFactory = new GeometryFactory(new PrecisionModel(), 4326); // WGS84坐标系
    }

    /// <summary>
    /// 从Shapefile加载道路数据
    /// </summary>
    public async Task<List<RoadGeometry>> LoadRoadDataFromShapefileAsync(string shapefilePath)
    {
        var roads = new List<RoadGeometry>();
        
        try
        {
            _fileLogService.WriteInfo($"开始从Shapefile加载道路数据: {shapefilePath}");

            if (!File.Exists(shapefilePath))
            {
                _fileLogService.WriteError($"Shapefile文件不存在: {shapefilePath}");
                return roads;
            }

            await Task.Run(() =>
            {
                using var reader = new ShapefileDataReader(shapefilePath, _geometryFactory);
                
                while (reader.Read())
                {
                    var geometry = reader.Geometry;
                    if (geometry is LineString lineString)
                    {
                        var road = new RoadGeometry
                        {
                            Geometry = lineString,
                            Length = CalculateLineStringLength(lineString)
                        };

                        // 读取属性信息
                        var header = reader.DbaseHeader;
                        for (int i = 0; i < header.NumFields; i++)
                        {
                            var fieldName = header.Fields[i].Name;
                            var value = reader.GetValue(i);
                            
                            switch (fieldName.ToUpper())
                            {
                                case "ROAD_ID":
                                case "ID":
                                    road.RoadId = value?.ToString() ?? "";
                                    break;
                                case "ROAD_NAME":
                                case "NAME":
                                    road.RoadName = value?.ToString() ?? "";
                                    break;
                                case "ROAD_TYPE":
                                case "TYPE":
                                    if (Enum.TryParse<RoadType>(value?.ToString(), out var roadType))
                                        road.RoadType = roadType;
                                    break;
                                case "REGION":
                                    road.Region = value?.ToString() ?? "";
                                    break;
                                default:
                                    road.Properties[fieldName] = value ?? "";
                                    break;
                            }
                        }

                        roads.Add(road);
                    }
                }
            });

            _fileLogService.WriteInfo($"成功加载{roads.Count}条道路数据");
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"加载Shapefile数据时发生错误: {ex.Message}");
        }

        return roads;
    }

    /// <summary>
    /// 从数据源加载设备位置数据
    /// </summary>
    public async Task<List<DeviceLocation>> LoadDeviceLocationsAsync(string deviceDataSource)
    {
        var devices = new List<DeviceLocation>();
        
        try
        {
            _fileLogService.WriteInfo($"开始加载设备位置数据: {deviceDataSource}");

            // 这里可以根据实际数据源类型实现不同的加载逻辑
            // 示例：从CSV文件加载
            if (File.Exists(deviceDataSource) && deviceDataSource.EndsWith(".csv"))
            {
                await Task.Run(() =>
                {
                    var lines = File.ReadAllLines(deviceDataSource, Encoding.UTF8);
                    for (int i = 1; i < lines.Length; i++) // 跳过标题行
                    {
                        var parts = lines[i].Split(',');
                        if (parts.Length >= 6)
                        {
                            var device = new DeviceLocation
                            {
                                DeviceId = parts[0],
                                DeviceName = parts[1],
                                Location = _geometryFactory.CreatePoint(new Coordinate(
                                    double.Parse(parts[2]), // 经度
                                    double.Parse(parts[3])  // 纬度
                                )),
                                DeviceType = Enum.TryParse<DeviceType>(parts[4], out var deviceType) ? deviceType : DeviceType.Traditional,
                                CoverageRadius = double.TryParse(parts[5], out var radius) ? radius : 100,
                                InstallTime = parts.Length > 6 && DateTime.TryParse(parts[6], out var installTime) ? installTime : DateTime.Now,
                                IsOnline = parts.Length > 7 && bool.TryParse(parts[7], out var isOnline) ? isOnline : true
                            };
                            devices.Add(device);
                        }
                    }
                });
            }

            _fileLogService.WriteInfo($"成功加载{devices.Count}个设备位置数据");
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"加载设备位置数据时发生错误: {ex.Message}");
        }

        return devices;
    }

    /// <summary>
    /// 计算道路渗透率分析
    /// </summary>
    public async Task<RoadPenetrationAnalysis> CalculateRoadPenetrationAsync(RoadGeometry road, List<DeviceLocation> devices)
    {
        return await Task.Run(() =>
        {
            var analysis = new RoadPenetrationAnalysis
            {
                RoadId = road.RoadId,
                RoadName = road.RoadName,
                TotalLength = road.Length
            };

            var coveredSegments = new List<LineString>();
            var smartDeviceCount = 0;
            var traditionalDeviceCount = 0;

            // 计算每个设备对道路的覆盖
            foreach (var device in devices)
            {
                if (IsDeviceCoveringRoad(road, device))
                {
                    var coveredSegment = GetCoveredRoadSegment(road, device);
                    if (coveredSegment != null)
                    {
                        coveredSegments.Add(coveredSegment);
                    }

                    if (device.DeviceType == DeviceType.Smart)
                        smartDeviceCount++;
                    else
                        traditionalDeviceCount++;
                }
            }

            analysis.SmartDeviceCount = smartDeviceCount;
            analysis.TraditionalDeviceCount = traditionalDeviceCount;
            analysis.CoveredSegments = coveredSegments;

            // 计算覆盖长度
            if (coveredSegments.Any())
            {
                var unionCovered = UnionGeometries(coveredSegments.Cast<Geometry>().ToList());
                analysis.CoveredLength = CalculateGeometryLength(unionCovered);
            }

            analysis.UncoveredLength = analysis.TotalLength - analysis.CoveredLength;
            analysis.CoveragePercentage = analysis.TotalLength > 0 ? (analysis.CoveredLength / analysis.TotalLength) * 100 : 0;

            // 计算重复覆盖
            var totalCoveredLength = coveredSegments.Sum(s => CalculateLineStringLength(s));
            analysis.OverlapLength = totalCoveredLength - analysis.CoveredLength;

            // 计算未覆盖段
            analysis.UncoveredSegments = GetUncoveredRoadSegments(road, coveredSegments);

            return analysis;
        });
    }

    /// <summary>
    /// 批量计算多条道路的渗透率分析
    /// </summary>
    public async Task<List<RoadPenetrationAnalysis>> CalculateBatchRoadPenetrationAsync(List<RoadGeometry> roads, List<DeviceLocation> devices)
    {
        var analyses = new List<RoadPenetrationAnalysis>();

        _fileLogService.WriteInfo($"开始批量计算{roads.Count}条道路的渗透率分析");

        var tasks = roads.Select(road => CalculateRoadPenetrationAsync(road, devices));
        analyses = (await Task.WhenAll(tasks)).ToList();

        _fileLogService.WriteInfo($"完成批量渗透率分析，共处理{analyses.Count}条道路");

        return analyses;
    }

    /// <summary>
    /// 计算区域渗透率统计
    /// </summary>
    public async Task<RegionPenetrationStatistics> CalculateRegionPenetrationStatisticsAsync(
        string regionName, 
        Polygon? regionBoundary, 
        List<RoadGeometry> roads, 
        List<DeviceLocation> devices)
    {
        return await Task.Run(() =>
        {
            var filteredRoads = roads;
            var filteredDevices = devices;

            // 如果有区域边界，过滤道路和设备
            if (regionBoundary != null)
            {
                filteredRoads = roads.Where(r => regionBoundary.Intersects(r.Geometry)).ToList();
                filteredDevices = devices.Where(d => IsPointInPolygon(d.Location, regionBoundary)).ToList();
            }

            var statistics = new RegionPenetrationStatistics
            {
                RegionName = regionName,
                RegionBoundary = regionBoundary,
                TotalRoadCount = filteredRoads.Count,
                TotalRoadLength = filteredRoads.Sum(r => r.Length) / 1000, // 转换为公里
                TotalDeviceCount = filteredDevices.Count,
                SmartDeviceCount = filteredDevices.Count(d => d.DeviceType == DeviceType.Smart)
            };

            // 计算每条道路的渗透率分析
            var roadAnalyses = new List<RoadPenetrationAnalysis>();
            foreach (var road in filteredRoads)
            {
                var analysis = CalculateRoadPenetrationAsync(road, filteredDevices).Result;
                roadAnalyses.Add(analysis);
            }

            statistics.RoadAnalyses = roadAnalyses;
            statistics.AverageCoverageRate = roadAnalyses.Any() ? roadAnalyses.Average(a => a.CoveragePercentage) : 0;
            statistics.AverageSmartDevicePenetrationRate = roadAnalyses.Any() ? roadAnalyses.Average(a => a.SmartDevicePenetrationRate) : 0;

            return statistics;
        });
    }

    /// <summary>
    /// 空间查询道路
    /// </summary>
    public async Task<List<RoadGeometry>> SpatialQueryRoadsAsync(SpatialQueryRequest queryRequest, List<RoadGeometry> roads)
    {
        return await Task.Run(() =>
        {
            var filteredRoads = roads.AsQueryable();

            // 区域过滤
            if (queryRequest.QueryRegion != null)
            {
                filteredRoads = filteredRoads.Where(r => queryRequest.QueryRegion.Intersects(r.Geometry));
            }

            // 道路类型过滤
            if (queryRequest.RoadTypes != null && queryRequest.RoadTypes.Any())
            {
                filteredRoads = filteredRoads.Where(r => queryRequest.RoadTypes.Contains(r.RoadType));
            }

            return filteredRoads.ToList();
        });
    }

    /// <summary>
    /// 空间查询设备
    /// </summary>
    public async Task<List<DeviceLocation>> SpatialQueryDevicesAsync(SpatialQueryRequest queryRequest, List<DeviceLocation> devices)
    {
        return await Task.Run(() =>
        {
            var filteredDevices = devices.AsQueryable();

            // 区域过滤
            if (queryRequest.QueryRegion != null)
            {
                filteredDevices = filteredDevices.Where(d => IsPointInPolygon(d.Location, queryRequest.QueryRegion));
            }

            // 设备类型过滤
            if (queryRequest.DeviceTypes != null && queryRequest.DeviceTypes.Any())
            {
                filteredDevices = filteredDevices.Where(d => queryRequest.DeviceTypes.Contains(d.DeviceType));
            }

            return filteredDevices.ToList();
        });
    }

    /// <summary>
    /// 计算道路与设备的空间关系
    /// </summary>
    public bool IsDeviceCoveringRoad(RoadGeometry road, DeviceLocation device)
    {
        try
        {
            var deviceBuffer = CreateBuffer(device.Location, device.CoverageRadius);
            return deviceBuffer.Intersects(road.Geometry);
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"计算道路与设备空间关系时发生错误: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 计算道路被设备覆盖的部分
    /// </summary>
    public LineString? GetCoveredRoadSegment(RoadGeometry road, DeviceLocation device)
    {
        try
        {
            var deviceBuffer = CreateBuffer(device.Location, device.CoverageRadius);
            var intersection = IntersectGeometries(road.Geometry, deviceBuffer);

            if (intersection is LineString lineString)
                return lineString;
            else if (intersection is MultiLineString multiLineString)
                return multiLineString.GetGeometryN(0) as LineString;

            return null;
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"计算道路覆盖段时发生错误: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 计算道路未被覆盖的部分
    /// </summary>
    public List<LineString> GetUncoveredRoadSegments(RoadGeometry road, List<LineString> coveredSegments)
    {
        var uncoveredSegments = new List<LineString>();

        try
        {
            if (!coveredSegments.Any())
            {
                uncoveredSegments.Add(road.Geometry);
                return uncoveredSegments;
            }

            var unionCovered = UnionGeometries(coveredSegments.Cast<Geometry>().ToList());
            var difference = DifferenceGeometries(road.Geometry, unionCovered);

            if (difference is LineString lineString)
            {
                uncoveredSegments.Add(lineString);
            }
            else if (difference is MultiLineString multiLineString)
            {
                for (int i = 0; i < multiLineString.NumGeometries; i++)
                {
                    if (multiLineString.GetGeometryN(i) is LineString segment)
                        uncoveredSegments.Add(segment);
                }
            }
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"计算未覆盖道路段时发生错误: {ex.Message}");
        }

        return uncoveredSegments;
    }

    /// <summary>
    /// 坐标系转换
    /// </summary>
    public Geometry TransformCoordinateSystem(Geometry geometry, int sourceSrid, int targetSrid)
    {
        // 这里需要使用ProjNet进行坐标系转换
        // 由于ProjNet的复杂性，这里提供基础框架
        try
        {
            // TODO: 实现具体的坐标系转换逻辑
            _fileLogService.WriteInfo($"坐标系转换: {sourceSrid} -> {targetSrid}");
            return geometry; // 临时返回原几何对象
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"坐标系转换时发生错误: {ex.Message}");
            return geometry;
        }
    }

    /// <summary>
    /// 创建缓冲区
    /// </summary>
    public Geometry CreateBuffer(Geometry geometry, double distance)
    {
        try
        {
            return geometry.Buffer(distance);
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"创建缓冲区时发生错误: {ex.Message}");
            return geometry;
        }
    }

    /// <summary>
    /// 计算两个几何对象的距离
    /// </summary>
    public double CalculateDistance(Geometry geometry1, Geometry geometry2)
    {
        try
        {
            return geometry1.Distance(geometry2);
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"计算距离时发生错误: {ex.Message}");
            return double.MaxValue;
        }
    }

    /// <summary>
    /// 判断点是否在多边形内
    /// </summary>
    public bool IsPointInPolygon(Point point, Polygon polygon)
    {
        try
        {
            return polygon.Contains(point);
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"判断点是否在多边形内时发生错误: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 计算线串的长度
    /// </summary>
    public double CalculateLineStringLength(LineString lineString)
    {
        try
        {
            return lineString.Length;
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"计算线串长度时发生错误: {ex.Message}");
            return 0;
        }
    }

    /// <summary>
    /// 合并多个几何对象
    /// </summary>
    public Geometry UnionGeometries(List<Geometry> geometries)
    {
        try
        {
            if (!geometries.Any())
                return _geometryFactory.CreateEmpty(Dimension.Curve);

            var result = geometries.First();
            for (int i = 1; i < geometries.Count; i++)
            {
                result = result.Union(geometries[i]);
            }
            return result;
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"合并几何对象时发生错误: {ex.Message}");
            return _geometryFactory.CreateEmpty(Dimension.Curve);
        }
    }

    /// <summary>
    /// 计算几何对象的交集
    /// </summary>
    public Geometry? IntersectGeometries(Geometry geometry1, Geometry geometry2)
    {
        try
        {
            return geometry1.Intersection(geometry2);
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"计算几何对象交集时发生错误: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 计算几何对象的差集
    /// </summary>
    public Geometry? DifferenceGeometries(Geometry geometry1, Geometry geometry2)
    {
        try
        {
            return geometry1.Difference(geometry2);
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"计算几何对象差集时发生错误: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 计算几何对象的长度（支持多种几何类型）
    /// </summary>
    private double CalculateGeometryLength(Geometry geometry)
    {
        try
        {
            if (geometry is LineString lineString)
                return lineString.Length;
            else if (geometry is MultiLineString multiLineString)
                return multiLineString.Length;
            else
                return 0;
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"计算几何对象长度时发生错误: {ex.Message}");
            return 0;
        }
    }
}
