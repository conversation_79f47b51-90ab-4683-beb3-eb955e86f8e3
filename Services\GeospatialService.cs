using APIFurion.Models;
using NetTopologySuite.Geometries;
using NetTopologySuite.IO;
using NetTopologySuite.Operation.Buffer;
using NetTopologySuite.Operation.Distance;
using System.Text;
using Furion.DependencyInjection;
using Microsoft.AspNetCore.Http.HttpResults;

namespace APIFurion.Services;

/// <summary>
/// 地理信息处理服务实现
/// </summary>
public class GeospatialService : IGeospatialService, IScoped
{
    private readonly IFileLogService _fileLogService;
    private readonly GeometryFactory _geometryFactory;

    public GeospatialService(IFileLogService fileLogService)
    {
        _fileLogService = fileLogService;
        _geometryFactory = new GeometryFactory(new PrecisionModel(), 4326); // WGS84坐标系
    }

    /// <summary>
    /// 从Shapefile加载道路数据
    /// </summary>
    public async Task<List<RoadGeometry>> LoadRoadDataFromShapefileAsync(string shapefilePath)
    {
        return await LoadRoadDataFromShapefileAsync(shapefilePath, null);
    }

    /// <summary>
    /// 从Shapefile加载道路数据（指定编码）
    /// </summary>
    /// <param name="shapefilePath">Shapefile文件路径</param>
    /// <param name="encoding">指定的编码，如果为null则自动检测</param>
    /// <returns>道路几何信息列表</returns>
    public async Task<List<RoadGeometry>> LoadRoadDataFromShapefileAsync(string shapefilePath, System.Text.Encoding? encoding)
    {
        var roads = new List<RoadGeometry>();

        try
        {
            _fileLogService.WriteInfo($"开始从Shapefile加载道路数据: {shapefilePath}");

            if (!File.Exists(shapefilePath))
            {
                _fileLogService.WriteError($"Shapefile文件不存在: {shapefilePath}");
                return roads;
            }

            await Task.Run(() =>
            {
                // 设置编码以正确读取中文字符
                System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);

                // 确定要使用的编码列表
                var encodings = encoding != null
                    ? new[] { encoding }
                    : new[]
                    {
                        System.Text.Encoding.GetEncoding("GBK"),     // 中文GBK编码
                        System.Text.Encoding.GetEncoding("GB2312"),  // 中文GB2312编码
                        System.Text.Encoding.UTF8,                   // UTF-8编码
                        System.Text.Encoding.GetEncoding("Big5"),    // 繁体中文Big5编码
                        System.Text.Encoding.Default                 // 系统默认编码
                    };

                ShapefileDataReader? reader = null;
                Exception? lastException = null;

                // 尝试不同的编码
                foreach (var encoding in encodings)
                {
                    try
                    {
                        reader = new ShapefileDataReader(shapefilePath, _geometryFactory, encoding);
                        _fileLogService.WriteInfo($"成功使用编码 {encoding.EncodingName} 读取Shapefile");
                        break;
                    }
                    catch (Exception ex)
                    {
                        lastException = ex;
                        reader?.Dispose();
                        reader = null;
                    }
                }

                if (reader == null)
                {
                    _fileLogService.WriteError($"无法使用任何编码读取Shapefile: {lastException?.Message}");
                    return;
                }

                using (reader)

                    while (reader.Read())
                    {
                        var geometry = reader.Geometry;
                        if (geometry is LineString lineString)
                        {
                            var road = new RoadGeometry
                            {
                                Geometry = lineString,
                                Length = CalculateLineStringLength(lineString)
                            };

                            // 读取属性信息
                            var header = reader.DbaseHeader;
                            for (int i = 0; i < header.NumFields; i++)
                            {
                                var fieldName = header.Fields[i].Name;
                                var value = reader.GetValue(i);

                                switch (fieldName.ToUpper())
                                {
                                    case "ROAD_ID":
                                    case "ID":
                                        road.RoadId = CleanString(value?.ToString() ?? "");
                                        break;
                                    case "ROAD_NAME":
                                    case "NAME":
                                        road.RoadName = CleanString(value?.ToString() ?? "");
                                        break;
                                    case "ROAD_TYPE":
                                    case "TYPE":
                                        if (Enum.TryParse<RoadType>(CleanString(value?.ToString()), out var roadType))
                                            road.RoadType = roadType;
                                        break;
                                    case "REGION":
                                        road.Region = CleanString(value?.ToString() ?? "");
                                        break;
                                    default:
                                        road.Properties[fieldName] = CleanString(value?.ToString() ?? "");
                                        break;
                                }
                            }

                            roads.Add(road);
                        }
                    }
            });

            _fileLogService.WriteInfo($"成功加载{roads.Count}条道路数据");
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"加载Shapefile数据时发生错误: {ex.Message}");
        }

        return roads;
    }

    public async Task<List<PenetrationResult>> CalculateBatchRoadPenetrationAsync(List<RoadGeometry> roads)
    {
        var resut = new List<PenetrationResult>();



        return resut;
    }

    /// <summary>
    /// 坐标系转换
    /// </summary>
    public Geometry TransformCoordinateSystem(Geometry geometry, int sourceSrid, int targetSrid)
    {
        // 这里需要使用ProjNet进行坐标系转换
        // 由于ProjNet的复杂性，这里提供基础框架
        try
        {
            // TODO: 实现具体的坐标系转换逻辑
            _fileLogService.WriteInfo($"坐标系转换: {sourceSrid} -> {targetSrid}");
            return geometry; // 临时返回原几何对象
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"坐标系转换时发生错误: {ex.Message}");
            return geometry;
        }
    }

    /// <summary>
    /// 创建缓冲区
    /// </summary>
    public Geometry CreateBuffer(Geometry geometry, double distance)
    {
        try
        {
            return geometry.Buffer(distance);
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"创建缓冲区时发生错误: {ex.Message}");
            return geometry;
        }
    }

    /// <summary>
    /// 计算两个几何对象的距离
    /// </summary>
    public double CalculateDistance(Geometry geometry1, Geometry geometry2)
    {
        try
        {
            return geometry1.Distance(geometry2);
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"计算距离时发生错误: {ex.Message}");
            return double.MaxValue;
        }
    }

    /// <summary>
    /// 判断点是否在多边形内
    /// </summary>
    public bool IsPointInPolygon(Point point, Polygon polygon)
    {
        try
        {
            return polygon.Contains(point);
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"判断点是否在多边形内时发生错误: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 计算线串的长度
    /// </summary>
    public double CalculateLineStringLength(LineString lineString)
    {
        try
        {
            return lineString.Length;
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"计算线串长度时发生错误: {ex.Message}");
            return 0;
        }
    }

    /// <summary>
    /// 合并多个几何对象
    /// </summary>
    public Geometry UnionGeometries(List<Geometry> geometries)
    {
        try
        {
            if (!geometries.Any())
                return _geometryFactory.CreateEmpty(Dimension.Curve);

            var result = geometries.First();
            for (int i = 1; i < geometries.Count; i++)
            {
                result = result.Union(geometries[i]);
            }
            return result;
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"合并几何对象时发生错误: {ex.Message}");
            return _geometryFactory.CreateEmpty(Dimension.Curve);
        }
    }

    /// <summary>
    /// 计算几何对象的交集
    /// </summary>
    public Geometry? IntersectGeometries(Geometry geometry1, Geometry geometry2)
    {
        try
        {
            return geometry1.Intersection(geometry2);
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"计算几何对象交集时发生错误: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 计算几何对象的差集
    /// </summary>
    public Geometry? DifferenceGeometries(Geometry geometry1, Geometry geometry2)
    {
        try
        {
            return geometry1.Difference(geometry2);
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"计算几何对象差集时发生错误: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 计算几何对象的长度（支持多种几何类型）
    /// </summary>
    private double CalculateGeometryLength(Geometry geometry)
    {
        try
        {
            if (geometry is LineString lineString)
                return lineString.Length;
            else if (geometry is MultiLineString multiLineString)
                return multiLineString.Length;
            else
                return 0;
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"计算几何对象长度时发生错误: {ex.Message}");
            return 0;
        }
    }

    /// <summary>
    /// 清理字符串，处理编码问题和空白字符
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>清理后的字符串</returns>
    private string CleanString(string input)
    {
        if (string.IsNullOrEmpty(input))
            return string.Empty;

        try
        {
            // 移除字符串前后的空白字符和null字符
            var cleaned = input.Trim().Trim('\0');

            // 如果字符串包含乱码字符，尝试重新编码
            if (ContainsGarbledCharacters(cleaned))
            {
                // 尝试从不同编码转换
                var encodings = new[]
                {
                    System.Text.Encoding.GetEncoding("GBK"),
                    System.Text.Encoding.GetEncoding("GB2312"),
                    System.Text.Encoding.GetEncoding("Big5")
                };

                foreach (var encoding in encodings)
                {
                    try
                    {
                        var bytes = System.Text.Encoding.Default.GetBytes(cleaned);
                        var converted = encoding.GetString(bytes);
                        if (!ContainsGarbledCharacters(converted))
                        {
                            _fileLogService.WriteInfo($"成功使用 {encoding.EncodingName} 编码转换字符串: {cleaned} -> {converted}");
                            return converted.Trim();
                        }
                    }
                    catch
                    {
                        // 忽略转换错误，继续尝试下一个编码
                    }
                }
            }

            return cleaned;
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"清理字符串时发生错误: {ex.Message}");
            return input;
        }
    }

    /// <summary>
    /// 检查字符串是否包含乱码字符
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>是否包含乱码</returns>
    private bool ContainsGarbledCharacters(string input)
    {
        if (string.IsNullOrEmpty(input))
            return false;

        // 检查是否包含常见的乱码字符
        var garbledPatterns = new[]
        {
            "?", "�", "□", "▯", // 常见的替换字符
            "\uFFFD" // Unicode替换字符
        };

        foreach (var pattern in garbledPatterns)
        {
            if (input.Contains(pattern))
                return true;
        }

        // 检查是否包含过多的非ASCII字符但不是有效的中文字符
        var nonAsciiCount = 0;
        var chineseCount = 0;

        foreach (char c in input)
        {
            if (c > 127)
            {
                nonAsciiCount++;
                // 检查是否是中文字符范围
                if ((c >= 0x4E00 && c <= 0x9FFF) || // CJK统一汉字
                    (c >= 0x3400 && c <= 0x4DBF) || // CJK扩展A
                    (c >= 0xF900 && c <= 0xFAFF))   // CJK兼容汉字
                {
                    chineseCount++;
                }
            }
        }

        // 如果有非ASCII字符但中文字符比例很低，可能是乱码
        if (nonAsciiCount > 0 && (double)chineseCount / nonAsciiCount < 0.5)
        {
            return true;
        }

        return false;
    }
}
