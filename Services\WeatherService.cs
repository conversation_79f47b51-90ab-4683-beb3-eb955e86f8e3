using APIFurion.Controllers;
using Furion.DependencyInjection;

namespace APIFurion.Services;

/// <summary>
/// 天气服务实现
/// </summary>
public class WeatherService : IWeatherService, ITransient
{
    private static readonly string[] Summaries = new[]
    {
        "Freezing", "Bracing", "Chi<PERSON>", "Cool", "Mild", "Warm", "<PERSON><PERSON><PERSON>", "Hot", "Sweltering", "Scorching"
    };

    /// <summary>
    /// 获取天气预报列表
    /// </summary>
    /// <param name="days">天数</param>
    /// <returns>天气预报列表</returns>
    public async Task<IEnumerable<WeatherForecast>> GetWeatherForecastAsync(int days = 5)
    {
        // 模拟异步操作
        await Task.Delay(100);

        return Enumerable.Range(1, days).Select(index => new WeatherForecast
        {
            Date = DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
            TemperatureC = Random.Shared.Next(-20, 55),
            Summary = Summaries[Random.Shared.Next(Summaries.Length)]
        });
    }

    /// <summary>
    /// 根据ID获取天气预报
    /// </summary>
    /// <param name="id">ID</param>
    /// <returns>天气预报</returns>
    public async Task<WeatherForecast?> GetWeatherForecastByIdAsync(int id)
    {
        // 模拟异步操作
        await Task.Delay(50);

        if (id < 1 || id > 10)
        {
            return null;
        }

        return new WeatherForecast
        {
            Date = DateOnly.FromDateTime(DateTime.Now.AddDays(id)),
            TemperatureC = Random.Shared.Next(-20, 55),
            Summary = Summaries[Random.Shared.Next(Summaries.Length)]
        };
    }
}
