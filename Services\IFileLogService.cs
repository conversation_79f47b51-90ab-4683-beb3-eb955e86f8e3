namespace APIFurion.Services;

/// <summary>
/// 文件日志服务接口
/// </summary>
public interface IFileLogService
{
    /// <summary>
    /// 写入访问日志
    /// </summary>
    /// <param name="clientIp">客户端IP</param>
    /// <param name="requestTime">请求时间</param>
    /// <param name="method">HTTP方法</param>
    /// <param name="path">请求路径</param>
    /// <param name="statusCode">状态码</param>
    /// <param name="responseTime">响应时间</param>
    void WriteAccessLog(string clientIp, DateTime requestTime, string method, string path, int statusCode, long responseTime);

    /// <summary>
    /// 写入通用日志
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息（可选）</param>
    void WriteLog(string level, string message, Exception? exception = null);

    /// <summary>
    /// 写入错误日志
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="exception">异常信息</param>
    void WriteError(string message, Exception? exception = null);

    /// <summary>
    /// 写入信息日志
    /// </summary>
    /// <param name="message">信息消息</param>
    void WriteInfo(string message);

    /// <summary>
    /// 写入警告日志
    /// </summary>
    /// <param name="message">警告消息</param>
    void WriteWarning(string message);
}
