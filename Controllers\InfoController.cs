using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using APIFurion.Options;
using APIFurion.Services;

namespace APIFurion.Controllers;

/// <summary>
/// 信息控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class InfoController : ControllerBase
{
    private readonly WeatherSettings _weatherSettings;
    private readonly IFileLogService _fileLogService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="weatherSettings">天气设置</param>
    /// <param name="fileLogService">文件日志服务</param>
    public InfoController(IOptions<WeatherSettings> weatherSettings, IFileLogService fileLogService)
    {
        _weatherSettings = weatherSettings.Value;
        _fileLogService = fileLogService;
    }

    /// <summary>
    /// 获取API信息
    /// </summary>
    /// <returns>API信息</returns>
    [HttpGet]
    public ActionResult<object> GetInfo()
    {
        // 记录接口开始调用的日志
        _fileLogService.WriteInfo($"GetInfo接口开始调用。");

        return Ok(new
        {
            ApiName = _weatherSettings.ApiName,
            Version = _weatherSettings.Version,
            DefaultDays = _weatherSettings.DefaultDays,
            MaxDays = _weatherSettings.MaxDays,
            ServerTime = DateTime.Now,
            Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"
        });
    }

    /// <summary>
    /// 健康检查
    /// </summary>
    /// <returns>健康状态</returns>
    [HttpGet("health")]
    public ActionResult<object> HealthCheck()
    {
        return Ok(new
        {
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Version = _weatherSettings.Version
        });
    }
}
