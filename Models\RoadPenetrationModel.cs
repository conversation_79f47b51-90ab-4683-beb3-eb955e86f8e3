namespace APIFurion.Models;

/// <summary>
/// 道路渗透率统计模型
/// </summary>
public class RoadPenetrationModel
{
    /// <summary>
    /// 道路ID
    /// </summary>
    public string RoadId { get; set; } = string.Empty;

    /// <summary>
    /// 道路名称
    /// </summary>
    public string RoadName { get; set; } = string.Empty;

    /// <summary>
    /// 道路长度（公里）
    /// </summary>
    public double RoadLength { get; set; }

    /// <summary>
    /// 智能设备数量
    /// </summary>
    public int SmartDeviceCount { get; set; }

    /// <summary>
    /// 传统设备数量
    /// </summary>
    public int TraditionalDeviceCount { get; set; }

    /// <summary>
    /// 总设备数量
    /// </summary>
    public int TotalDeviceCount => SmartDeviceCount + TraditionalDeviceCount;

    /// <summary>
    /// 渗透率（百分比）
    /// </summary>
    public double PenetrationRate => TotalDeviceCount > 0 ? (double)SmartDeviceCount / TotalDeviceCount * 100 : 0;

    /// <summary>
    /// 设备密度（设备数/公里）
    /// </summary>
    public double DeviceDensity => RoadLength > 0 ? TotalDeviceCount / RoadLength : 0;

    /// <summary>
    /// 统计时间
    /// </summary>
    public DateTime StatisticsTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 道路类型
    /// </summary>
    public RoadType RoadType { get; set; }

    /// <summary>
    /// 所属区域
    /// </summary>
    public string Region { get; set; } = string.Empty;
}

/// <summary>
/// 道路类型枚举
/// </summary>
public enum RoadType
{
    /// <summary>
    /// 高速公路
    /// </summary>
    Highway = 1,

    /// <summary>
    /// 国道
    /// </summary>
    NationalRoad = 2,

    /// <summary>
    /// 省道
    /// </summary>
    ProvincialRoad = 3,

    /// <summary>
    /// 县道
    /// </summary>
    CountyRoad = 4,

    /// <summary>
    /// 城市道路
    /// </summary>
    UrbanRoad = 5
}

/// <summary>
/// 渗透率统计请求模型
/// </summary>
public class PenetrationStatisticsRequest
{
    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 道路类型（可选）
    /// </summary>
    public RoadType? RoadType { get; set; }

    /// <summary>
    /// 区域（可选）
    /// </summary>
    public string? Region { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 20;
}

/// <summary>
/// 渗透率统计响应模型
/// </summary>
public class PenetrationStatisticsResponse
{
    /// <summary>
    /// 道路渗透率列表
    /// </summary>
    public List<RoadPenetrationModel> Roads { get; set; } = new();

    /// <summary>
    /// 总记录数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 平均渗透率
    /// </summary>
    public double AveragePenetrationRate { get; set; }

    /// <summary>
    /// 最高渗透率
    /// </summary>
    public double MaxPenetrationRate { get; set; }

    /// <summary>
    /// 最低渗透率
    /// </summary>
    public double MinPenetrationRate { get; set; }

    /// <summary>
    /// 总智能设备数
    /// </summary>
    public int TotalSmartDevices { get; set; }

    /// <summary>
    /// 总设备数
    /// </summary>
    public int TotalDevices { get; set; }
}


