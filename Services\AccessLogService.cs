using System.Collections.Concurrent;

namespace APIFurion.Services;

/// <summary>
/// 访问日志服务实现
/// </summary>
public class AccessLogService : IAccessLogService
{
    private readonly ILogger<AccessLogService> _logger;
    private static readonly ConcurrentDictionary<string, int> _ipAccessCount = new();
    private static readonly ConcurrentQueue<AccessLogEntry> _recentAccess = new();
    private const int MaxRecentEntries = 1000;

    public AccessLogService(ILogger<AccessLogService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 记录API访问日志
    /// </summary>
    public void LogApiAccess(string clientIp, string method, string path, int statusCode, long responseTime)
    {
        var timestamp = DateTime.Now;

        // 记录到日志文件
        _logger.LogInformation(
            "API访问 - 时间: {Timestamp:yyyy-MM-dd HH:mm:ss}, IP: {ClientIp}, 方法: {Method}, 路径: {Path}, 状态码: {StatusCode}, 响应时间: {ResponseTime}ms",
            timestamp, clientIp, method, path, statusCode, responseTime);

        // 统计IP访问次数
        _ipAccessCount.AddOrUpdate(clientIp, 1, (key, oldValue) => oldValue + 1);

        // 保存最近的访问记录
        var entry = new AccessLogEntry
        {
            Timestamp = timestamp,
            ClientIp = clientIp,
            Method = method,
            Path = path,
            StatusCode = statusCode,
            ResponseTime = responseTime
        };

        _recentAccess.Enqueue(entry);

        // 保持队列大小
        while (_recentAccess.Count > MaxRecentEntries)
        {
            _recentAccess.TryDequeue(out _);
        }
    }

    /// <summary>
    /// 获取访问统计信息
    /// </summary>
    public async Task<object> GetAccessStatisticsAsync()
    {
        await Task.CompletedTask; // 模拟异步操作

        var topIps = _ipAccessCount
            .OrderByDescending(x => x.Value)
            .Take(10)
            .ToDictionary(x => x.Key, x => x.Value);

        var recentAccessList = _recentAccess
            .TakeLast(50)
            .OrderByDescending(x => x.Timestamp)
            .ToList();

        return new
        {
            TotalUniqueIps = _ipAccessCount.Count,
            TotalRequests = _ipAccessCount.Values.Sum(),
            TopIpAddresses = topIps,
            RecentAccess = recentAccessList.Select(x => new
            {
                x.Timestamp,
                x.ClientIp,
                x.Method,
                x.Path,
                x.StatusCode,
                x.ResponseTime,
                UserAgent = x.UserAgent.Length > 100 ? x.UserAgent.Substring(0, 100) + "..." : x.UserAgent
            })
        };
    }
}

/// <summary>
/// 访问日志条目
/// </summary>
public class AccessLogEntry
{
    public DateTime Timestamp { get; set; }
    public string ClientIp { get; set; } = string.Empty;
    public string Method { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public int StatusCode { get; set; }
    public long ResponseTime { get; set; }
    public string UserAgent { get; set; } = string.Empty;
}
