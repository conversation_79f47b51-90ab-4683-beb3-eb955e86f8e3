using APIFurion.Controllers;

namespace APIFurion.Services;

/// <summary>
/// 天气服务接口
/// </summary>
public interface IWeatherService
{
    /// <summary>
    /// 获取天气预报列表
    /// </summary>
    /// <param name="days">天数</param>
    /// <returns>天气预报列表</returns>
    Task<IEnumerable<WeatherForecast>> GetWeatherForecastAsync(int days = 5);

    /// <summary>
    /// 根据ID获取天气预报
    /// </summary>
    /// <param name="id">ID</param>
    /// <returns>天气预报</returns>
    Task<WeatherForecast?> GetWeatherForecastByIdAsync(int id);
}
