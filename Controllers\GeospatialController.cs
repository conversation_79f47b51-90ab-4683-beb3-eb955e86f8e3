using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using APIFurion.Models;
using APIFurion.Services;
using APIFurion.Options;

namespace APIFurion.Controllers;

/// <summary>
/// 地理信息处理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class GeospatialController : ControllerBase
{
    private readonly IGeospatialService _geospatialService;
    private readonly IRoadPenetrationService _roadPenetrationService;
    private readonly IFileLogService _fileLogService;
    private readonly AppSettings _appSettings;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="geospatialService">地理信息服务</param>
    /// <param name="roadPenetrationService">道路渗透率服务</param>
    /// <param name="fileLogService">文件日志服务</param>
    /// <param name="appSettings">应用程序设置</param>
    public GeospatialController(
        IGeospatialService geospatialService,
        IRoadPenetrationService roadPenetrationService,
        IFileLogService fileLogService,
        IOptions<AppSettings> appSettings)
    {
        _geospatialService = geospatialService;
        _roadPenetrationService = roadPenetrationService;
        _fileLogService = fileLogService;
        _appSettings = appSettings.Value;
    }

    /// <summary>
    /// 从Shapefile分析道路渗透率
    /// </summary>
    /// <param name="request">分析请求参数</param>
    /// <returns>道路渗透率分析结果</returns>
    [HttpPost("analyze-road-penetration")]
    public async Task<ActionResult<object>> AnalyzeRoadPenetrationFromShapefile([FromBody] RoadPenetrationAnalysisRequest request)
    {
        try
        {
            _fileLogService.WriteInfo("开始执行Shapefile道路渗透率分析接口");

            if (string.IsNullOrEmpty(request.RoadLayerPath))
            {
                return BadRequest(new { Message = "道路图层路径不能为空" });
            }

            if (string.IsNullOrEmpty(request.DeviceDataSource))
            {
                return BadRequest(new { Message = "设备数据源不能为空" });
            }

            var analyses = await _roadPenetrationService.AnalyzeRoadPenetrationFromShapefileAsync(
                request.RoadLayerPath,
                request.DeviceDataSource,
                request.RegionName ?? "");

            return Ok(new
            {
                Success = true,
                Data = analyses,
                TotalRoads = analyses.Count,
                AverageCoverageRate = analyses.Any() ? analyses.Average(a => a.CoveragePercentage) : 0,
                AverageSmartDevicePenetrationRate = analyses.Any() ? analyses.Average(a => a.SmartDevicePenetrationRate) : 0,
                MainDBStr = _appSettings.MainDBStr,
                Timestamp = DateTime.Now
            });
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"Shapefile道路渗透率分析时发生错误: {ex.Message}");
            return StatusCode(500, new { Message = "分析过程中发生错误", Error = ex.Message });
        }
    }

    /// <summary>
    /// 计算区域渗透率统计
    /// </summary>
    /// <param name="request">区域统计请求参数</param>
    /// <returns>区域渗透率统计结果</returns>
    [HttpPost("region-statistics")]
    public async Task<ActionResult<object>> CalculateRegionStatistics([FromBody] RegionStatisticsRequest request)
    {
        try
        {
            _fileLogService.WriteInfo($"开始执行区域渗透率统计接口 - 区域: {request.RegionName}");

            if (string.IsNullOrEmpty(request.RegionName))
            {
                return BadRequest(new { Message = "区域名称不能为空" });
            }

            var statistics = await _roadPenetrationService.CalculateRegionStatisticsAsync(
                request.RegionName,
                request.RoadLayerPath ?? _appSettings.RoadLayerPath ?? "",
                request.DeviceDataSource ?? "");

            return Ok(new
            {
                Success = true,
                Data = statistics,
                MainDBStr = _appSettings.MainDBStr,
                Timestamp = DateTime.Now
            });
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"区域渗透率统计时发生错误: {ex.Message}");
            return StatusCode(500, new { Message = "统计过程中发生错误", Error = ex.Message });
        }
    }

    /// <summary>
    /// 加载道路数据
    /// </summary>
    /// <param name="request">加载道路数据请求</param>
    /// <returns>道路数据</returns>
    [HttpPost("load-roads")]
    public async Task<ActionResult<object>> LoadRoadData([FromBody] LoadRoadDataRequest request)
    {
        try
        {
            _fileLogService.WriteInfo($"开始加载道路数据 - 文件: {request.ShapefilePath}");

            if (string.IsNullOrEmpty(request.ShapefilePath))
            {
                return BadRequest(new { Message = "Shapefile路径不能为空" });
            }

            var roads = await _geospatialService.LoadRoadDataFromShapefileAsync(request.ShapefilePath);

            return Ok(new
            {
                Success = true,
                Data = roads.Select(r => new
                {
                    r.RoadId,
                    r.RoadName,
                    r.Length,
                    r.RoadType,
                    r.Region,
                    GeometryType = r.Geometry.GeometryType,
                    CoordinateCount = r.Geometry.Coordinates.Length
                }),
                TotalCount = roads.Count,
                TotalLength = roads.Sum(r => r.Length),
                MainDBStr = _appSettings.MainDBStr,
                Timestamp = DateTime.Now
            });
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"加载道路数据时发生错误: {ex.Message}");
            return StatusCode(500, new { Message = "加载过程中发生错误", Error = ex.Message });
        }
    }

    /// <summary>
    /// 加载设备位置数据
    /// </summary>
    /// <param name="request">加载设备数据请求</param>
    /// <returns>设备位置数据</returns>
    [HttpPost("load-devices")]
    public async Task<ActionResult<object>> LoadDeviceData([FromBody] LoadDeviceDataRequest request)
    {
        try
        {
            _fileLogService.WriteInfo($"开始加载设备数据 - 数据源: {request.DeviceDataSource}");

            if (string.IsNullOrEmpty(request.DeviceDataSource))
            {
                return BadRequest(new { Message = "设备数据源不能为空" });
            }

            var devices = await _geospatialService.LoadDeviceLocationsAsync(request.DeviceDataSource);

            return Ok(new
            {
                Success = true,
                Data = devices.Select(d => new
                {
                    d.DeviceId,
                    d.DeviceName,
                    d.DeviceType,
                    d.CoverageRadius,
                    d.InstallTime,
                    d.IsOnline,
                    Longitude = d.Location.X,
                    Latitude = d.Location.Y
                }),
                TotalCount = devices.Count,
                SmartDeviceCount = devices.Count(d => d.DeviceType == DeviceType.Smart),
                TraditionalDeviceCount = devices.Count(d => d.DeviceType == DeviceType.Traditional),
                MainDBStr = _appSettings.MainDBStr,
                Timestamp = DateTime.Now
            });
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"加载设备数据时发生错误: {ex.Message}");
            return StatusCode(500, new { Message = "加载过程中发生错误", Error = ex.Message });
        }
    }
}
