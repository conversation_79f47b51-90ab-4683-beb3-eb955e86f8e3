using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using APIFurion.Models;
using APIFurion.Services;
using APIFurion.Options;

namespace APIFurion.Controllers;

/// <summary>
/// 地理信息处理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class GeospatialController : ControllerBase
{
    private readonly IGeospatialService _geospatialService;
    private readonly IRoadPenetrationService _roadPenetrationService;
    private readonly IFileLogService _fileLogService;
    private readonly AppSettings _appSettings;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="geospatialService">地理信息服务</param>
    /// <param name="roadPenetrationService">道路渗透率服务</param>
    /// <param name="fileLogService">文件日志服务</param>
    /// <param name="appSettings">应用程序设置</param>
    public GeospatialController(
        IGeospatialService geospatialService,
        IRoadPenetrationService roadPenetrationService,
        IFileLogService fileLogService,
        IOptions<AppSettings> appSettings)
    {
        _geospatialService = geospatialService;
        _roadPenetrationService = roadPenetrationService;
        _fileLogService = fileLogService;
        _appSettings = appSettings.Value;
    }

    /// <summary>
    /// 加载道路数据
    /// </summary>
    /// <param name="request">加载道路数据请求</param>
    /// <returns>道路数据</returns>
    [HttpPost("GetRoad")]
    public async Task<ActionResult<object>> LoadRoadData([FromBody] LoadRoadDataRequest request)
    {
        try
        {
            _fileLogService.WriteInfo($"开始加载道路数据 - 文件: {request.ShapefilePath}");

            if (string.IsNullOrEmpty(request.ShapefilePath))
            {
                return BadRequest(new { Message = "Shapefile路径不能为空" });
            }

            var roads = await _geospatialService.LoadRoadDataFromShapefileAsync(request.ShapefilePath);

            return Ok(new
            {
                Success = true,
                Data = roads.Select(r => new
                {
                    r.RoadId,
                    r.RoadName,
                    r.Length,
                    r.RoadType,
                    r.Region,
                    GeometryType = r.Geometry.GeometryType,
                    CoordinateCount = r.Geometry.Coordinates.Length
                }),
                TotalCount = roads.Count,
                TotalLength = roads.Sum(r => r.Length),
                MainDBStr = _appSettings.MainDBStr,
                Timestamp = DateTime.Now
            });
        }
        catch (Exception ex)
        {
            _fileLogService.WriteError($"加载道路数据时发生错误: {ex.Message}");
            return StatusCode(500, new { Message = "加载过程中发生错误", Error = ex.Message });
        }
    }


    [HttpPost("GetPenetrationStatistics")]
    public async Task<ActionResult<object>> GetPenetrationStatistics([FromBody] FileRoadDataRequest request)
    {
        _fileLogService.WriteInfo("开始执行统计道路渗透率接口。");

        var content = await _roadPenetrationService.AnalyzeRoadPenetrationFromShapefileAsync(request);
        return Ok(new
        {
            Data = content,
            MainDBStr = _appSettings.MainDBStr,
            Timestamp = DateTime.Now
        });
    }
}
