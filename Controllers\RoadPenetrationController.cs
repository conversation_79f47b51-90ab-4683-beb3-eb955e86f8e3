using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using APIFurion.Models;
using APIFurion.Services;
using APIFurion.Options;

namespace APIFurion.Controllers;

/// <summary>
/// 道路渗透率统计控制器
/// </summary>
[ApiController]
[Route("[controller]")]
public class RoadPenetrationController : ControllerBase
{
    private readonly IRoadPenetrationService _roadPenetrationService;
    private readonly IFileLogService _fileLogService;
    private readonly AppSettings _appSettings;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="roadPenetrationService">道路渗透率服务</param>
    /// <param name="fileLogService">文件日志服务</param>
    /// <param name="appSettings">应用程序设置</param>
    public RoadPenetrationController(IRoadPenetrationService roadPenetrationService, IFileLogService fileLogService, IOptions<AppSettings> appSettings)
    {
        _roadPenetrationService = roadPenetrationService;
        _fileLogService = fileLogService;
        _appSettings = appSettings.Value;
    }






}
