using Microsoft.AspNetCore.Mvc;
using APIFurion.Models;
using APIFurion.Services;

namespace APIFurion.Controllers;

/// <summary>
/// 道路渗透率统计控制器
/// </summary>
[ApiController]
[Route("[controller]")]
public class RoadPenetrationController : ControllerBase
{
    private readonly IRoadPenetrationService _roadPenetrationService;
    private readonly IFileLogService _fileLogService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="roadPenetrationService">道路渗透率服务</param>
    /// <param name="fileLogService">文件日志服务</param>
    public RoadPenetrationController(IRoadPenetrationService roadPenetrationService, IFileLogService fileLogService)
    {
        _roadPenetrationService = roadPenetrationService;
        _fileLogService = fileLogService;
    }


    [HttpPost("GetPenetrationStatistics")]
    public async Task<ActionResult<object>> GetPenetrationStatistics([FromBody] PenetrationStatisticsRequest request)
    {
        _fileLogService.WriteInfo("开始执行统计道路渗透率接口。");

        var content = await _roadPenetrationService.GetPenetrationStatisticsAsync(request);

        return Ok(new
        {
            Date = ""
        });
    }



}
